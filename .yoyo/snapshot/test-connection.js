#!/usr/bin/env node

// 简单的 OpenAI API 连接测试
const https = require('https');
require('dotenv').config();

async function testConnection() {
  console.log('🧪 测试 OpenAI API 连接...');
  
  const postData = JSON.stringify({
    model: "gpt-3.5-turbo",
    messages: [{"role": "user", "content": "Hello"}],
    max_tokens: 5
  });
  
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'api.openai.com',
      port: 443,
      path: '/v1/chat/completions',
      method: 'POST',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Length': Buffer.byteLength(postData)
      }
    };
    
    const req = https.request(options, (res) => {
      console.log('✅ 连接成功! 状态码:', res.statusCode);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.log('❌ 连接失败:', err.message);
      reject(err);
    });
    
    req.on('timeout', () => {
      console.log('❌ 连接超时');
      req.destroy();
      reject(new Error('Timeout'));
    });
    
    req.write(postData);
    req.end();
  });
}

testConnection().then(() => {
  console.log('🎉 测试完成，可以尝试使用应用了！');
  process.exit(0);
}).catch((error) => {
  console.log('⚠️  连接仍有问题，建议：');
  console.log('1. 检查网络稳定性');
  console.log('2. 稍后重试');
  console.log('3. 使用不同的网络环境');
  process.exit(1);
}); 