// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Card {
  id              String   @id @default(cuid())
  title           String
  sourceType      String   // 'url' or 'text'
  sourceData      String   // 原始URL或文本内容
  originalContent String   // 抓取到的HTML内容或处理后的文本
  aiNoteMarkdown  String   // AI生成的Markdown笔记
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  @@map("cards")
}

model SavedNote {
  id               String   @id @default(cuid())
  title            String
  originalContent  String   // 原始内容
  structuredNotes  String   // 初始的结构化笔记
  integratedNotes  String   // 整合对话后的最终笔记
  sourceType       String   // 'url' or 'text'
  sourceData       String   // 原始URL或文本内容
  tags             String?  // 标签，JSON格式存储
  
  // FastGPT 同步管理字段
  fastgptCollectionId   String?  // FastGPT中的collection ID
  fastgptSyncStatus     String   @default("pending") // pending, syncing, synced, failed
  fastgptTrainingStatus String?  // waiting, training, trained, failed
  fastgptLastSyncAt     DateTime? // 最后同步时间
  fastgptErrorMessage   String?  // 同步错误信息
  
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  @@map("saved_notes")
}
