# 沉淀 - AI记忆沉淀器

> 专为信息过载的普通人设计的AI记忆沉淀器，让知识的获取、理解和沉淀过程合而为一。

## ✨ 特性

- 🧠 **智能分析**: GPT-4驱动的内容提炼和结构化笔记生成
- 🌐 **多源输入**: 支持网页URL和文本内容处理
- 💬 **AI对话**: 基于内容的智能问答和深度讨论
- 📚 **知识库**: 自动保存和管理处理过的内容
- 🎨 **优雅界面**: ChatGPT风格的极简设计
- ⚡ **实时响应**: 流畅的标签页和聊天体验

## 🚀 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
```bash
git clone <repository-url>
cd knowledge-cards
```

2. **安装依赖**
```bash
npm install
```

3. **配置环境变量**
```bash
cp .env.example .env.local
```
编辑 `.env.local` 文件，添加你的 OpenAI API 密钥：
```env
OPENAI_API_KEY="your_openai_api_key_here"
```

4. **设置数据库**
```bash
npx prisma generate
npx prisma db push
```

5. **启动开发服务器**
```bash
npm run dev
```

6. **访问应用**
打开 [http://localhost:3000](http://localhost:3000)

## 📖 使用指南

### 基本工作流程
1. 在主页输入框中粘贴网页URL或文本内容
2. 系统自动提取并分析内容，生成AI笔记
3. 在右侧AI助手面板查看结构化笔记
4. 使用聊天功能与AI深入讨论内容
5. 点击"存入知识库"保存重要会话
6. 在知识库页面管理和回顾历史记录

### 主要功能
- **内容处理**: 智能提取网页核心内容，过滤广告和无关信息
- **AI笔记**: 自动生成包含核心观点、关键信息和实用价值的结构化笔记
- **智能对话**: 基于原文和AI笔记的上下文问答
- **知识管理**: 保存、检索和管理处理过的内容
- **FastGPT集成**: 支持同步到外部知识库系统

## 🏗️ 技术架构

- **前端**: Next.js 15 + React + Tailwind CSS 2.2.9
- **后端**: Next.js API Routes
- **数据库**: SQLite + Prisma ORM
- **AI服务**: OpenAI GPT-4
- **状态管理**: Zustand
- **内容提取**: @mozilla/readability
- **样式**: @tailwindcss/typography

## 🧪 测试

运行自动化测试：
```bash
# 基本功能测试
node test-basic-functionality.js

# 完整功能测试
node test-complete-functionality.js

# 前端功能测试
open test-frontend.html
```

## 📁 项目结构

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API路由
│   ├── archive/           # 知识库页面
│   └── page.tsx           # 主页
├── components/            # React组件
│   ├── layout/           # 布局组件
│   └── ui/               # UI组件
├── lib/                  # 工具库
│   └── store.ts          # 状态管理
└── generated/            # 生成的文件
```

## 🔧 配置

详细的环境变量配置请参考 `.env.local` 文件。主要配置项：

- `OPENAI_API_KEY`: OpenAI API密钥
- `DATABASE_URL`: 数据库连接字符串
- `AI_SYSTEM_PROMPT`: AI系统提示词
- `FASTGPT_API_URL`: FastGPT集成URL

## 📊 性能

- ⚡ 内容处理响应时间 < 10秒
- 🧠 高质量AI分析（GPT-4）
- 💾 本地SQLite存储，无需外部依赖
- 📱 响应式设计，支持各种设备

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React框架
- [OpenAI](https://openai.com/) - AI服务
- [Tailwind CSS](https://tailwindcss.com/) - CSS框架
- [Prisma](https://prisma.io/) - 数据库ORM
- [@mozilla/readability](https://github.com/mozilla/readability) - 内容提取
