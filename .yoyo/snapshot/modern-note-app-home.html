<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>沉淀 - 智能笔记系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #6366f1;
            --primary-light: #818cf8;
            --primary-dark: #4f46e5;
            --bg-primary: #fafafa;
            --bg-secondary: #ffffff;
            --text-primary: #1e293b;
            --text-secondary: #64748b;
            --accent: #f59e0b;
            --glass: rgba(255, 255, 255, 0.7);
            --glass-dark: rgba(30, 41, 59, 0.03);
            --glass-border: rgba(148, 163, 184, 0.2);
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.08), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: var(--bg-primary);
            color: var(--text-primary);
            height: 100vh;
            overflow: hidden;
            position: relative;
        }

        /* 动态背景 */
        .bg-animation {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 0;
            overflow: hidden;
        }

        .gradient-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(80px);
            opacity: 0.15;
            animation: float 20s infinite ease-in-out;
        }

        .gradient-orb:nth-child(1) {
            width: 600px;
            height: 600px;
            background: radial-gradient(circle, #6366f1 0%, transparent 70%);
            top: -200px;
            right: -100px;
        }

        .gradient-orb:nth-child(2) {
            width: 400px;
            height: 400px;
            background: radial-gradient(circle, #f59e0b 0%, transparent 70%);
            bottom: -150px;
            left: -50px;
            animation-delay: -5s;
        }

        .gradient-orb:nth-child(3) {
            width: 500px;
            height: 500px;
            background: radial-gradient(circle, #818cf8 0%, transparent 70%);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(-50px, 50px) scale(1.1); }
            50% { transform: translate(50px, -30px) scale(0.9); }
            75% { transform: translate(-30px, -50px) scale(1.05); }
        }

        /* 网格背景 */
        .grid-bg {
            position: fixed;
            width: 100%;
            height: 100%;
            background-image: 
                linear-gradient(rgba(148, 163, 184, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(148, 163, 184, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            z-index: 1;
        }

        /* 主容器 */
        .main-container {
            position: relative;
            z-index: 10;
            height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 2rem;
        }

        /* 新的图标设计 */
        .app-icon {
            width: 120px;
            height: 120px;
            margin-bottom: 2rem;
            position: relative;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .app-icon:hover {
            transform: scale(1.1);
        }

        .icon-container {
            width: 100%;
            height: 100%;
            background: var(--glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .icon-container::before {
            content: '';
            position: absolute;
            inset: -2px;
            background: linear-gradient(45deg, var(--primary), var(--primary-light), var(--accent));
            border-radius: 30px;
            z-index: -1;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .app-icon:hover .icon-container::before {
            opacity: 0.8;
        }

        .app-icon:hover .icon-container {
            border-color: transparent;
            background: rgba(255, 255, 255, 0.9);
        }

        .icon-content {
            position: relative;
            width: 60px;
            height: 60px;
        }

        /* 新图标 - 流动的数据结构 */
        .data-flow {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .node {
            position: absolute;
            width: 12px;
            height: 12px;
            background: var(--primary);
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(99, 102, 241, 0.5);
        }

        .node:nth-child(1) { top: 10%; left: 50%; transform: translateX(-50%); }
        .node:nth-child(2) { top: 40%; left: 20%; }
        .node:nth-child(3) { top: 40%; right: 20%; }
        .node:nth-child(4) { bottom: 10%; left: 30%; }
        .node:nth-child(5) { bottom: 10%; right: 30%; }

        .connection {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary), transparent);
            transform-origin: left center;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; }
            50% { opacity: 1; }
        }

        /* 标题样式 */
        .app-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: 0.05em;
        }

        .app-subtitle {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: 3rem;
            font-weight: 400;
        }

        /* 输入区域 */
        .input-container {
            width: 100%;
            max-width: 600px;
            margin-bottom: 2rem;
            position: relative;
        }

        .input-wrapper {
            position: relative;
            background: var(--glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            padding: 1rem 1.5rem;
            transition: all 0.3s ease;
            box-shadow: var(--shadow);
        }

        .input-wrapper:hover {
            box-shadow: var(--shadow-lg);
        }

        .input-wrapper:focus-within {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-lg);
        }

        .input-field {
            width: 100%;
            background: transparent;
            border: none;
            outline: none;
            font-size: 1rem;
            color: var(--text-primary);
            padding-right: 3rem;
        }

        .input-field::placeholder {
            color: var(--text-secondary);
        }

        .input-icon {
            position: absolute;
            right: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 24px;
            height: 24px;
            color: var(--primary);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .input-icon:hover {
            transform: translateY(-50%) scale(1.2) rotate(15deg);
        }

        /* 操作按钮 */
        .action-button {
            padding: 1rem 3rem;
            background: linear-gradient(135deg, var(--primary) 0%, var(--primary-light) 100%);
            color: white;
            border: none;
            border-radius: 50px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            box-shadow: var(--shadow-lg);
        }

        .action-button::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .action-button:hover::before {
            width: 300px;
            height: 300px;
        }

        .action-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
        }

        /* 快捷功能 */
        .quick-actions {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 20;
        }

        .quick-action {
            width: 48px;
            height: 48px;
            background: var(--glass);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            box-shadow: var(--shadow);
        }

        .quick-action:hover {
            background: var(--primary);
            transform: translateY(-4px);
            box-shadow: 0 8px 20px rgba(99, 102, 241, 0.3);
            border-color: transparent;
        }

        .quick-action svg {
            width: 24px;
            height: 24px;
            color: var(--text-secondary);
            transition: color 0.3s ease;
        }

        .quick-action:hover svg {
            color: white;
        }

        /* 悬浮提示 */
        .tooltip {
            position: absolute;
            bottom: 120%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--text-primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            pointer-events: none;
            box-shadow: var(--shadow-lg);
        }

        .quick-action:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

        .tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border: 6px solid transparent;
            border-top-color: var(--text-primary);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .app-title {
                font-size: 2.5rem;
            }

            .app-subtitle {
                font-size: 1rem;
            }

            .input-container {
                max-width: 90%;
            }

            .action-button {
                padding: 0.875rem 2.5rem;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景 -->
    <div class="bg-animation">
        <div class="gradient-orb"></div>
        <div class="gradient-orb"></div>
        <div class="gradient-orb"></div>
    </div>
    
    <!-- 网格背景 -->
    <div class="grid-bg"></div>

    <!-- 主容器 -->
    <div class="main-container">
        <!-- 新图标 -->
        <div class="app-icon">
            <div class="icon-container">
                <div class="icon-content">
                    <div class="data-flow">
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                        <div class="node"></div>
                        <svg style="position: absolute; width: 100%; height: 100%; top: 0; left: 0;">
                            <line class="connection" x1="30" y1="6" x2="12" y2="24" stroke="url(#gradient)" stroke-width="2"/>
                            <line class="connection" x1="30" y1="6" x2="48" y2="24" stroke="url(#gradient)" stroke-width="2"/>
                            <line class="connection" x1="12" y1="24" x2="18" y2="48" stroke="url(#gradient)" stroke-width="2"/>
                            <line class="connection" x1="48" y1="24" x2="42" y2="48" stroke="url(#gradient)" stroke-width="2"/>
                            <defs>
                                <linearGradient id="gradient">
                                    <stop offset="0%" stop-color="transparent"/>
                                    <stop offset="50%" stop-color="#6366f1"/>
                                    <stop offset="100%" stop-color="transparent"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- 标题 -->
        <h1 class="app-title">沉淀</h1>
        <p class="app-subtitle">智能内容分析与结构化笔记生成</p>

        <!-- 输入区域 -->
        <div class="input-container">
            <div class="input-wrapper">
                <input type="text" class="input-field" placeholder="输入网页链接或粘贴文本内容...">
                <svg class="input-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
            </div>
        </div>

        <!-- 主操作按钮 -->
        <button class="action-button">开始分析</button>
    </div>

    <!-- 快捷功能 -->
    <div class="quick-actions">
        <div class="quick-action">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
            </svg>
            <span class="tooltip">我的笔记</span>
        </div>
        <div class="quick-action">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span class="tooltip">历史记录</span>
        </div>
        <div class="quick-action">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            <span class="tooltip">设置</span>
        </div>
    </div>

    <script>
        // 添加交互效果
        const inputField = document.querySelector('.input-field');
        const actionButton = document.querySelector('.action-button');
        const inputIcon = document.querySelector('.input-icon');

        // 输入框回车触发
        inputField.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                startAnalysis();
            }
        });

        // 按钮点击效果
        actionButton.addEventListener('click', startAnalysis);
        inputIcon.addEventListener('click', startAnalysis);

        function startAnalysis() {
            const value = inputField.value.trim();
            if (value) {
                // 添加加载动画
                actionButton.textContent = '分析中...';
                actionButton.style.pointerEvents = 'none';
                
                // 模拟分析过程
                setTimeout(() => {
                    actionButton.textContent = '开始分析';
                    actionButton.style.pointerEvents = 'auto';
                    // 这里可以添加实际的分析逻辑
                }, 2000);
            }
        }

        // 图标点击动画
        const appIcon = document.querySelector('.app-icon');
        appIcon.addEventListener('click', function() {
            this.style.animation = 'spin 0.6s ease';
            setTimeout(() => {
                this.style.animation = '';
            }, 600);
        });

        // 添加旋转动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes spin {
                0% { transform: scale(1) rotate(0deg); }
                50% { transform: scale(1.1) rotate(180deg); }
                100% { transform: scale(1) rotate(360deg); }
            }
        `;
        document.head.appendChild(style);

        // 动态连线动画
        const connections = document.querySelectorAll('.connection');
        connections.forEach((line, index) => {
            line.style.animationDelay = `${index * 0.2}s`;
        });
    </script>
</body>
</html>