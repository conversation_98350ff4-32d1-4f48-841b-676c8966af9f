import { Readability } from '@mozilla/readability'
import { JSDOM } from 'jsdom'
import axios from 'axios'
import { chromium } from 'playwright'

export interface ScrapedContent {
  title: string
  content: string
  textContent: string
  url: string
}

/**
 * 尝试使用轻量级方法抓取网页内容
 */
async function fastScrape(url: string): Promise<ScrapedContent | null> {
  try {
    console.log('尝试快速抓取:', url)
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Cache-Control': 'no-cache'
      },
      timeout: 15000,
      maxRedirects: 5
    })

    console.log('快速抓取响应状态:', response.status)
    const dom = new JSDOM(response.data, { url })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()

    if (article && article.content && article.textContent) {
      console.log('快速抓取成功, 内容长度:', article.textContent.length)
      return {
        title: article.title || 'Untitled',
        content: article.content,
        textContent: article.textContent,
        url
      }
    }

    console.log('快速抓取失败: Readability 无法解析有效内容')
    return null
  } catch (error) {
    console.error('快速抓取失败:', error instanceof Error ? error.message : String(error))
    return null
  }
}

/**
 * 使用 Playwright 进行重型抓取（处理 JS 渲染页面）
 */
async function heavyScrape(url: string): Promise<ScrapedContent | null> {
  let browser = null
  try {
    console.log('尝试重型抓取 (Playwright):', url)
    browser = await chromium.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox'] 
    })
    const page = await browser.newPage()
    
    // 设置更真实的浏览器环境
    await page.setExtraHTTPHeaders({
      'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    await page.setViewportSize({ width: 1920, height: 1080 })
    
    await page.goto(url, { 
      waitUntil: 'domcontentloaded',
      timeout: 30000 
    })
    
    // 等待页面完全加载
    await page.waitForTimeout(3000)
    
    const content = await page.content()
    const dom = new JSDOM(content, { url })
    const reader = new Readability(dom.window.document)
    const article = reader.parse()

    if (article && article.content && article.textContent) {
      console.log('重型抓取成功, 内容长度:', article.textContent.length)
      return {
        title: article.title || 'Untitled',
        content: article.content,
        textContent: article.textContent,
        url
      }
    }

    console.log('重型抓取失败: Readability 无法解析有效内容')
    return null
  } catch (error) {
    console.error('重型抓取失败:', error instanceof Error ? error.message : String(error))
    return null
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

/**
 * 智能抓取网页内容（先尝试快速方法，失败后使用重型方法）
 */
export async function scrapeWebPage(url: string): Promise<ScrapedContent> {
  // 验证 URL 格式
  try {
    new URL(url)
  } catch {
    throw new Error('无效的 URL 格式')
  }

  console.log('开始抓取网页:', url)
  
  // 先尝试快速抓取
  let result = await fastScrape(url)
  
  if (!result) {
    console.log('快速抓取失败，尝试重型抓取...')
    // 如果快速抓取失败，使用 Playwright
    result = await heavyScrape(url)
  }

  if (!result) {
    throw new Error('无法从网页抓取到有效内容。该网页可能：1) 禁止爬虫访问 2) 需要登录 3) 使用了复杂的反爬虫机制')
  }

  console.log('抓取成功:', result.title)
  return result
} 