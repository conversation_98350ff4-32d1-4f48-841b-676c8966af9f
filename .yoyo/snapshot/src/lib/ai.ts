import OpenAI from 'openai'
import { 
  searchKnowledgeBase, 
  getReflectionSearchOptions, 
  formatSearchResults,
  type SearchResult 
} from '@/lib/fastgpt'

// 改进的 OpenAI 客户端配置
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
  timeout: 60000, // 60 秒超时
  maxRetries: 3,  // 最多重试 3 次
})

// 重试机制的包装函数
async function withRetry<T>(operation: () => Promise<T>, maxRetries = 3): Promise<T> {
  let lastError: Error
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation()
    } catch (error) {
      lastError = error as Error
      
      // 检查是否是网络连接错误
      if (error instanceof Error && 
          (error.message.includes('ECONNRESET') || 
           error.message.includes('Connection error') ||
           error.message.includes('fetch failed'))) {
        
        console.warn(`网络请求失败 (尝试 ${attempt}/${maxRetries}):`, error.message)
        
        if (attempt < maxRetries) {
          // 指数退避：1s, 2s, 4s
          const delay = Math.pow(2, attempt - 1) * 1000
          console.log(`等待 ${delay}ms 后重试...`)
          await new Promise(resolve => setTimeout(resolve, delay))
          continue
        }
      }
      
      // 对于非网络错误，直接抛出
      throw error
    }
  }
  
  throw lastError!
}

export interface KnowledgeCard {
  title: string
  content: string
}

/**
 * 将网页内容转换为知识卡片
 */
export async function generateKnowledgeCards(content: string, url: string): Promise<KnowledgeCard[]> {
  const prompt = `
请将以下文章内容分解成多个独立的知识卡片。每张卡片应该包含一个明确的知识点或概念。

要求：
1. 每张卡片都应该是自包含的，读者无需其他上下文就能理解
2. 卡片标题简洁明了，体现核心概念
3. 卡片内容详细但精炼，包含关键信息和要点
4. 返回JSON数组格式，每个元素包含 "title" 和 "content" 字段
5. 至少生成3张卡片，最多10张卡片

文章内容：
${content}

请返回JSON格式的知识卡片数组：
`

  try {
    const response = await openai.chat.completions.create({
      model: "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: "你是一个专业的知识整理助手，擅长将复杂内容分解为清晰的知识卡片。"
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    })

    const result = response.choices[0]?.message?.content
    if (!result) {
      throw new Error('No response from OpenAI')
    }

    // 尝试解析JSON响应
    try {
      const cards = JSON.parse(result) as KnowledgeCard[]
      return cards.filter(card => card.title && card.content)
    } catch (parseError) {
      // 如果直接解析失败，尝试提取JSON部分
      const jsonMatch = result.match(/\[[\s\S]*\]/)
      if (jsonMatch) {
        const cards = JSON.parse(jsonMatch[0]) as KnowledgeCard[]
        return cards.filter(card => card.title && card.content)
      }
      throw new Error('Failed to parse knowledge cards from AI response')
    }
  } catch (error) {
    console.error('Error generating knowledge cards:', error)
    throw new Error('Failed to generate knowledge cards')
  }
}

/**
 * 将网页内容以流式方式转换为结构化笔记
 * @param content 文章原文
 * @returns OpenAI Stream
 */
export async function streamKnowledgeCards(content: string) {
  // 使用环境变量中的系统提示词，如果没有则使用默认值
  const systemPrompt = process.env.AI_SYSTEM_PROMPT || '你是一个专业的知识整理助手，擅长将复杂内容提炼成结构清晰、格式优美的Markdown笔记。'

  const basePrompt = process.env.STRUCTURED_NOTES_PROMPT || "请将以下文章内容提炼成一份结构化的Markdown笔记。文章内容："

  const prompt = `${basePrompt}
${content}

请开始生成Markdown格式的结构化笔记：`

  // 对于流式响应，不能使用 withRetry 包装，因为流式响应不能重试
  // 直接返回 OpenAI 流，但在更高层处理错误
  try {
    return await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      stream: true,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.5'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '20000')
    })
  } catch (error) {
    console.error('创建 OpenAI 流失败:', error)
    throw error
  }
}

/**
 * 沉淀模式 - AI问答功能 (仅基于当前文档上下文)
 * 用于用户阅读特定文档时的问答，不涉及外部知识库
 */
export async function askQuestion(question: string, context: string): Promise<string> {
  const systemPrompt = process.env.CHAT_SYSTEM_PROMPT || "你是一个专业的AI助手，基于给定的文章内容回答用户问题。请保持回答的准确性和相关性，只基于提供的上下文内容回答，不要添加外部信息。"

  const prompt = `基于以下文章内容回答用户问题：

文章内容：
${context}

用户问题：${question}

请仅基于上述文章内容回答问题，如果文章中没有相关信息，请明确说明。`

  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.3'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '20000')
    })

    return response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'
  } catch (error) {
    console.error('Error answering question:', error)
    throw new Error('Failed to get AI response')
  }
}

/**
 * 沉淀模式 - 支持对话历史的AI问答功能
 * 基于当前文档内容进行多轮对话，保持对话连贯性
 */
export async function askQuestionWithContext(
  question: string,
  documentContext: string,
  chatHistory: Array<{role: 'user' | 'assistant', content: string}> = []
): Promise<string> {
  const systemPrompt = process.env.ASK_MODE_SYSTEM_PROMPT || process.env.CHAT_SYSTEM_PROMPT || "你是一个知识渊博的AI助手，可以基于文档内容进行自然对话。你可以自由地回答用户的问题，提供见解和补充信息，不必严格局限于文档内容。你应该参考对话历史保持连贯性，并根据用户的需求提供有价值的回答。"

  // 构建消息数组
  const messages: Array<{role: 'system' | 'user' | 'assistant', content: string}> = [
    {
      role: "system",
      content: `${systemPrompt}

当前文档内容：
${documentContext}

你可以：
- 基于文档内容回答问题
- 提供相关的背景知识和见解
- 与用户进行深入的讨论
- 根据对话历史保持连贯性
- 在适当时候提供超出文档范围的有价值信息

请自然地与用户对话，提供有帮助的回答。`
    }
  ]

  // 添加对话历史（最多保留最近8轮对话以控制token使用）
  const recentHistory = chatHistory.slice(-8)
  for (const msg of recentHistory) {
    messages.push({
      role: msg.role,
      content: msg.content
    })
  }

  // 添加当前问题
  messages.push({
    role: "user",
    content: question
  })

  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.3'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '20000')
    })

    return response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'
  } catch (error) {
    console.error('Error answering question with context:', error)
    throw new Error('Failed to get AI response')
  }
}

/**
 * Agent模式 - 支持对话历史的AI问答功能，同时生成结构化笔记修改建议
 * 基于当前文档内容进行多轮对话，并提供结构化笔记的改进建议
 */
export async function askQuestionWithContextAgent(
  question: string,
  documentContext: string,
  chatHistory: Array<{role: 'user' | 'assistant', content: string}> = []
): Promise<{answer: string, noteModifications?: any}> {
  const systemPrompt = process.env.AGENT_MODE_SYSTEM_PROMPT || "你是一个智能的知识助手，可以自然地与用户对话讨论文档内容。你的任务是：1. 自然回答用户的任何问题，不局限于文档内容；2. 在对话过程中，如果涉及到值得记录的新见解、补充信息或重要讨论点，则建议将这些内容整理到结构化笔记中。你应该像一个知识渊博的助手一样自由对话，同时智能地识别哪些对话内容值得保存到笔记中。"

  // 构建消息数组
  const messages: Array<{role: 'system' | 'user' | 'assistant', content: string}> = [
    {
      role: "system",
      content: `${systemPrompt}

当前文档的原始内容：
${documentContext}

作为知识助手，你可以：
- 自然地回答用户的任何问题，包括但不限于文档内容
- 提供额外的见解、背景知识、相关信息
- 进行深入的讨论和分析
- 帮助用户更好地理解和应用知识

在对话过程中，如果出现以下情况，建议将内容添加到结构化笔记中：
- 用户提出了新的观点或见解
- 讨论中产生了重要的补充信息
- 发现了值得记录的相关知识点
- 用户希望保存某些讨论内容

如果需要修改笔记，请在回答末尾添加修改建议，格式如下：

---MODIFICATIONS---
{
  "hasModifications": true/false,
  "modifications": [
    {
      "type": "add" | "modify" | "delete", 
      "section": "章节名称",
      "content": "具体内容",
      "reason": "修改原因"
    }
  ]
}
---END_MODIFICATIONS---

重要：如果只是一般性对话或回答问题，不需要强制添加修改建议。只有在真正有价值的内容需要保存时才建议修改。`
    }
  ]

  // 添加对话历史（最多保留最近8轮对话以控制token使用）
  const recentHistory = chatHistory.slice(-8)
  for (const msg of recentHistory) {
    messages.push({
      role: msg.role,
      content: msg.content
    })
  }

  // 添加当前问题
  messages.push({
    role: "user",
    content: question
  })

  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.3'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '20000')
    })

    const fullResponse = response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'

    // 解析回答和修改建议
    const modificationMatch = fullResponse.match(/---MODIFICATIONS---([\s\S]*?)---END_MODIFICATIONS---/)
    let answer = fullResponse
    let noteModifications = null

    if (modificationMatch) {
      // 提取回答部分（去除修改建议部分）
      answer = fullResponse.replace(/---MODIFICATIONS---[\s\S]*?---END_MODIFICATIONS---/, '').trim()

      try {
        // 解析修改建议
        const modificationText = modificationMatch[1].trim()
        noteModifications = JSON.parse(modificationText)
      } catch (parseError) {
        console.error('Failed to parse note modifications:', parseError)
        // 如果解析失败，仍然返回回答，但不包含修改建议
      }
    }

    return {
      answer,
      noteModifications
    }
  } catch (error) {
    console.error('Error in agent mode question answering:', error)
    throw new Error('Failed to get AI response')
  }
}

/**
 * Agent模式 - 流式版本，支持对话历史和修改建议
 * 返回流式响应，需要在客户端解析修改建议
 */
export async function askQuestionWithContextAgentStreaming(
  question: string,
  documentContext: string,
  chatHistory: Array<{role: 'user' | 'assistant', content: string}> = []
) {
  const systemPrompt = process.env.AGENT_MODE_SYSTEM_PROMPT || "你是一个智能的知识助手，可以自然地与用户对话讨论文档内容。你的任务是：1. 自然回答用户的任何问题，不局限于文档内容；2. 在对话过程中，如果涉及到值得记录的新见解、补充信息或重要讨论点，则建议将这些内容整理到结构化笔记中。你应该像一个知识渊博的助手一样自由对话，同时智能地识别哪些对话内容值得保存到笔记中。"

  // 构建消息数组
  const messages: Array<{role: 'system' | 'user' | 'assistant', content: string}> = [
    {
      role: "system",
      content: `${systemPrompt}

当前文档的原始内容：
${documentContext}

作为知识助手，你可以：
- 自然地回答用户的任何问题，包括但不限于文档内容
- 提供额外的见解、背景知识、相关信息
- 进行深入的讨论和分析
- 帮助用户更好地理解和应用知识

在对话过程中，如果出现以下情况，建议将内容添加到结构化笔记中：
- 用户提出了新的观点或见解
- 讨论中产生了重要的补充信息
- 发现了值得记录的相关知识点
- 用户希望保存某些讨论内容

如果需要修改笔记，请在回答末尾添加修改建议，格式如下：

---MODIFICATIONS---
{
  "hasModifications": true/false,
  "modifications": [
    {
      "type": "add" | "modify" | "delete", 
      "section": "章节名称",
      "content": "具体内容",
      "reason": "修改原因"
    }
  ]
}
---END_MODIFICATIONS---

重要：如果只是一般性对话或回答问题，不需要强制添加修改建议。只有在真正有价值的内容需要保存时才建议修改。`
    }
  ]

  // 添加对话历史（最多保留最近8轮对话以控制token使用）
  const recentHistory = chatHistory.slice(-8)
  for (const msg of recentHistory) {
    messages.push({
      role: msg.role,
      content: msg.content
    })
  }

  // 添加当前问题
  messages.push({
    role: "user",
    content: question
  })

  try {
    return await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages,
      stream: true,
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.3'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '20000')
    })
  } catch (error) {
    console.error('Error in agent streaming mode:', error)
    throw new Error('Failed to get streaming AI response')
  }
}

/**
 * 沉思模式 - AI问答功能 (基于FastGPT知识库)
 * 用于全局知识库搜索和问答，可以访问所有已同步的知识
 */
export async function askQuestionWithKnowledgeBase(question: string): Promise<string> {
  const systemPrompt = process.env.REFLECTION_SYSTEM_PROMPT || "你是一个拥有丰富知识库的AI助手。你可以基于知识库中的内容回答用户问题，并提供准确、有深度的回答。如果知识库中没有相关信息，请诚实说明。"

  // 从 FastGPT 知识库检索相关内容，使用环境变量配置的搜索选项
  let kbContext = ''
  let hasKnowledgeBase = false
  
  try {
    const searchOptions = getReflectionSearchOptions()
    const kbResults = await searchKnowledgeBase(question, searchOptions)
    
    if (Array.isArray(kbResults) && kbResults.length > 0) {
      hasKnowledgeBase = true
      kbContext = formatSearchResults(kbResults)
    }
  } catch (error) {
    console.error('[Reflection Mode] Knowledge base search failed:', error)
  }

  let prompt: string
  if (hasKnowledgeBase) {
    prompt = `基于以下知识库内容回答用户问题：

知识库检索结果：
${kbContext}

用户问题：${question}

请基于知识库内容提供准确、有深度的回答。如果需要整合多个知识片段，请进行合理的综合分析。`
  } else {
    prompt = `用户问题：${question}

知识库中暂未找到相关内容。请基于你的基础知识提供帮助，并说明这不是基于知识库的回答。`
  }

  try {
    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || "gpt-4o-mini",
      messages: [
        {
          role: "system",
          content: systemPrompt
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.4'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1500')
    })

    const answer = response.choices[0]?.message?.content || '抱歉，我无法回答这个问题。'
    
    // 如果使用了知识库，在回答末尾添加说明
    if (hasKnowledgeBase) {
      return `${answer}\n\n---\n*此回答基于知识库内容生成*`
    } else {
      return `${answer}\n\n---\n*知识库中暂无相关内容，以上为基础回答*`
    }
    
  } catch (error) {
    console.error('Error in reflection mode question answering:', error)
    throw new Error('Failed to get AI response from knowledge base')
  }
}