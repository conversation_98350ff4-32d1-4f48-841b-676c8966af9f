import { NoteReference, TextSegment } from './store'

/**
 * 定位标记解析器
 * 解析AI生成的带定位标记的内容，提取映射关系并生成干净的显示内容
 */

// 定位标记的正则表达式
const LOCATION_MARKUP_REGEX = /\[LOC:([^\]]+)\](.*?)\[\/LOC\]/gs

// 解析结果接口
export interface ParsedNoteContent {
  cleanContent: string           // 移除标记后的干净内容
  noteReferences: NoteReference[] // 提取的映射关系
  hasLocationMarks: boolean      // 是否包含定位标记
}

// 定位片段接口
export interface LocationSegment {
  segmentId: string
  content: string
  startIndex: number
  endIndex: number
  noteId: string
}

export class LocationMarkupParser {
  private static instance: LocationMarkupParser

  static getInstance(): LocationMarkupParser {
    if (!LocationMarkupParser.instance) {
      LocationMarkupParser.instance = new LocationMarkupParser()
    }
    return LocationMarkupParser.instance
  }

  /**
   * 解析带定位标记的AI生成内容
   */
  parseContent(
    aiGeneratedContent: string, 
    textSegments: TextSegment[] = []
  ): ParsedNoteContent {
    const locationSegments: LocationSegment[] = []
    let cleanContent = aiGeneratedContent
    let noteIdCounter = 0

    // 提取所有定位标记
    const matches = Array.from(aiGeneratedContent.matchAll(LOCATION_MARKUP_REGEX))
    
    if (matches.length === 0) {
      return {
        cleanContent: aiGeneratedContent,
        noteReferences: [],
        hasLocationMarks: false
      }
    }

    // 从后往前替换，避免索引偏移问题
    for (let i = matches.length - 1; i >= 0; i--) {
      const match = matches[i]
      const [fullMatch, segmentId, content] = match
      const startIndex = match.index!
      const endIndex = startIndex + fullMatch.length

      // 记录定位片段信息
      locationSegments.unshift({
        segmentId: segmentId.trim(),
        content: content.trim(),
        startIndex,
        endIndex,
        noteId: `note_${noteIdCounter++}`
      })

      // 替换标记为纯内容
      cleanContent = cleanContent.substring(0, startIndex) + 
                    content + 
                    cleanContent.substring(endIndex)
    }

    // 生成映射关系
    const noteReferences = this.generateNoteReferences(locationSegments, textSegments)

    return {
      cleanContent,
      noteReferences,
      hasLocationMarks: true
    }
  }

  /**
   * 生成笔记映射关系
   */
  private generateNoteReferences(
    locationSegments: LocationSegment[],
    textSegments: TextSegment[]
  ): NoteReference[] {
    const noteReferences: NoteReference[] = []
    const segmentMap = new Map(textSegments.map(seg => [seg.id, seg]))

    for (const locSeg of locationSegments) {
      const targetSegment = segmentMap.get(locSeg.segmentId)
      
      if (targetSegment) {
        // 找到对应的文本片段
        noteReferences.push({
          noteId: locSeg.noteId,
          noteContent: locSeg.content,
          sourceSegments: [locSeg.segmentId],
          confidence: 1.0, // 精确匹配，置信度最高
          mappingType: 'exact',
          position: {
            startLine: this.calculateLineNumber(targetSegment.startOffset, textSegments),
            endLine: this.calculateLineNumber(targetSegment.endOffset, textSegments),
            startChar: targetSegment.startOffset,
            endChar: targetSegment.endOffset
          }
        })
      } else {
        // 如果找不到精确匹配，尝试模糊匹配
        const fuzzyMatches = this.findFuzzyMatches(locSeg.segmentId, textSegments)
        if (fuzzyMatches.length > 0) {
          noteReferences.push({
            noteId: locSeg.noteId,
            noteContent: locSeg.content,
            sourceSegments: fuzzyMatches.map(seg => seg.id),
            confidence: 0.7, // 模糊匹配，置信度较低
            mappingType: 'semantic',
            position: {
              startLine: this.calculateLineNumber(fuzzyMatches[0].startOffset, textSegments),
              endLine: this.calculateLineNumber(fuzzyMatches[fuzzyMatches.length - 1].endOffset, textSegments),
              startChar: fuzzyMatches[0].startOffset,
              endChar: fuzzyMatches[fuzzyMatches.length - 1].endOffset
            }
          })
        }
      }
    }

    return noteReferences
  }

  /**
   * 模糊匹配文本片段
   */
  private findFuzzyMatches(segmentId: string, textSegments: TextSegment[]): TextSegment[] {
    // 尝试通过ID模式匹配
    const idPattern = segmentId.replace(/[^0-9]/g, '')
    if (idPattern) {
      const matches = textSegments.filter(seg => seg.id.includes(idPattern))
      if (matches.length > 0) return matches.slice(0, 1) // 返回第一个匹配
    }

    // 尝试通过内容哈希匹配
    const hashPattern = segmentId.substring(segmentId.lastIndexOf('_') + 1)
    if (hashPattern.length >= 4) {
      const matches = textSegments.filter(seg => seg.hash.startsWith(hashPattern))
      if (matches.length > 0) return matches.slice(0, 1)
    }

    return []
  }

  /**
   * 计算字符偏移量对应的行号
   */
  private calculateLineNumber(offset: number, textSegments: TextSegment[]): number {
    // 简化实现：基于片段索引估算行号
    for (let i = 0; i < textSegments.length; i++) {
      if (textSegments[i].startOffset <= offset && offset <= textSegments[i].endOffset) {
        return i + 1
      }
    }
    return 1
  }

  /**
   * 实时解析流式内容中的定位标记
   */
  parseStreamingContent(
    streamingContent: string,
    textSegments: TextSegment[] = []
  ): ParsedNoteContent {
    // 检查是否有完整的定位标记
    const completeMatches = Array.from(streamingContent.matchAll(LOCATION_MARKUP_REGEX))
    
    if (completeMatches.length === 0) {
      // 检查是否有未完成的标记
      const incompleteMatch = streamingContent.match(/\[LOC:[^\]]*\].*$/s)
      if (incompleteMatch) {
        // 有未完成的标记，暂时不处理
        return {
          cleanContent: streamingContent,
          noteReferences: [],
          hasLocationMarks: false
        }
      }
      
      return {
        cleanContent: streamingContent,
        noteReferences: [],
        hasLocationMarks: false
      }
    }

    // 处理完整的标记
    return this.parseContent(streamingContent, textSegments)
  }

  /**
   * 验证segment_id是否有效
   */
  validateSegmentId(segmentId: string, textSegments: TextSegment[]): boolean {
    return textSegments.some(seg => seg.id === segmentId)
  }

  /**
   * 生成定位标记（用于测试或手动创建）
   */
  generateLocationMarkup(segmentId: string, content: string): string {
    return `[LOC:${segmentId}]${content}[/LOC]`
  }

  /**
   * 检查内容是否包含定位标记
   */
  hasLocationMarks(content: string): boolean {
    return LOCATION_MARKUP_REGEX.test(content)
  }

  /**
   * 获取所有定位标记的segment_id列表
   */
  extractSegmentIds(content: string): string[] {
    const matches = Array.from(content.matchAll(LOCATION_MARKUP_REGEX))
    return matches.map(match => match[1].trim())
  }

  /**
   * 清理内容中的所有定位标记
   */
  cleanContent(content: string): string {
    return content.replace(LOCATION_MARKUP_REGEX, '$2')
  }
}

export const locationMarkupParser = LocationMarkupParser.getInstance()
