/*
  Helper utilities for interacting with FastGPT OpenAPI.
  Implements comprehensive knowledge base sync management:
  1. syncNoteToFastGPT: Push a markdown note to a specified knowledge base (dataset).
  2. searchKnowledgeBase: Perform a similarity search in the knowledge base.
  3. deleteFromFastGPT: Remove a collection from knowledge base.
  4. getCollectionStatus: Check training status of a collection.
  5. listCollections: Get all collections in the dataset.

  Environment variables required:
  FASTGPT_API_URL   - Base URL of FastGPT backend, e.g. "https://api.fastgpt.in" (no trailing slash)
  FASTGPT_API_KEY   - FastGPT API key with dataset read/write permission.
  FASTGPT_DATASET_ID - Target dataset (knowledge base) id to sync to / search in.

  Note: For production use, consider caching/retry/back-off and error categorisation.
*/
import type { SavedNote } from '@/lib/store'

// 基础配置
const BASE_URL = process.env.FASTGPT_API_URL?.replace(/\/$/, '') || ''
const API_KEY = process.env.FASTGPT_API_KEY || ''
const DATASET_ID = process.env.FASTGPT_DATASET_ID || ''

// 搜索配置 - 默认值
const SEARCH_DEFAULT_LIMIT = parseInt(process.env.FASTGPT_SEARCH_DEFAULT_LIMIT || '6000')
const SEARCH_DEFAULT_SIMILARITY = parseFloat(process.env.FASTGPT_SEARCH_DEFAULT_SIMILARITY || '0.1')
const SEARCH_DEFAULT_MODE = process.env.FASTGPT_SEARCH_DEFAULT_MODE || 'fullTextRecall'
const SEARCH_DEFAULT_RERANK = process.env.FASTGPT_SEARCH_DEFAULT_RERANK === 'true'

// 沉思模式搜索配置
const REFLECTION_SEARCH_LIMIT = parseInt(process.env.FASTGPT_REFLECTION_SEARCH_LIMIT || '4000')
const REFLECTION_SEARCH_SIMILARITY = parseFloat(process.env.FASTGPT_REFLECTION_SEARCH_SIMILARITY || '0.3')
const REFLECTION_SEARCH_MODE = process.env.FASTGPT_REFLECTION_SEARCH_MODE || 'mixedRecall'
const REFLECTION_SEARCH_RERANK = process.env.FASTGPT_REFLECTION_SEARCH_RERANK === 'true'
const REFLECTION_SEARCH_EXTENSION_QUERY = process.env.FASTGPT_REFLECTION_SEARCH_EXTENSION_QUERY === 'true'
const REFLECTION_SEARCH_EXTENSION_MODEL = process.env.FASTGPT_REFLECTION_SEARCH_EXTENSION_MODEL || 'gpt-4o-mini'
const REFLECTION_SEARCH_EXTENSION_BG = process.env.FASTGPT_REFLECTION_SEARCH_EXTENSION_BG || '基于用户的知识库内容，优化搜索查询以获得最相关的结果'

// 同步配置
const SYNC_TRAINING_TYPE = process.env.FASTGPT_SYNC_TRAINING_TYPE || 'chunk'
const SYNC_CHUNK_SETTING_MODE = process.env.FASTGPT_SYNC_CHUNK_SETTING_MODE || 'auto'
const SYNC_METADATA_SOURCE = process.env.FASTGPT_SYNC_METADATA_SOURCE || 'knowledge-cards-app'

// Collection管理配置
const COLLECTION_LIST_LIMIT = parseInt(process.env.FASTGPT_COLLECTION_LIST_LIMIT || '1000')
const COLLECTION_NAME_MAX_LENGTH = parseInt(process.env.FASTGPT_COLLECTION_NAME_MAX_LENGTH || '60')

// 搜索结果配置
const SEARCH_MAX_RESULTS = parseInt(process.env.FASTGPT_SEARCH_MAX_RESULTS || '8')
const SEARCH_SHOW_SCORE = process.env.FASTGPT_SEARCH_SHOW_SCORE === 'true'

if (!BASE_URL || !API_KEY || !DATASET_ID) {
  console.warn('[FastGPT] Missing required ENV. The FastGPT integration will be disabled.')
}

interface FastGPTResponse<T> {
  code: number
  message: string
  data: T
}

export interface CollectionInfo {
  _id: string
  name: string
  trainingType: string
  status: 'waiting' | 'training' | 'trained' | 'failed'
  createTime: string
  updateTime: string
}

export interface SearchResult {
  id: string
  q: string              // 主要数据
  a: string              // 辅助数据  
  datasetId: string
  collectionId: string
  sourceName: string
  sourceId: string
  score: any             // 相关度分数 (可能是数组或数字)
  chunkIndex?: number
  indexes?: any[]
  tokens?: number
}

export interface SearchOptions {
  limit?: number                           // 最大 tokens 数量，最多 20000
  similarity?: number                      // 最低相关度（0~1）
  searchMode?: 'embedding' | 'fullTextRecall' | 'mixedRecall'  // 搜索模式
  usingReRank?: boolean                   // 使用重排
  datasetSearchUsingExtensionQuery?: boolean  // 使用问题优化
  datasetSearchExtensionModel?: string    // 问题优化模型
  datasetSearchExtensionBg?: string       // 问题优化背景描述
}

/**
 * 获取默认搜索选项
 */
export function getDefaultSearchOptions(): SearchOptions {
  return {
    limit: SEARCH_DEFAULT_LIMIT,
    similarity: SEARCH_DEFAULT_SIMILARITY,
    searchMode: SEARCH_DEFAULT_MODE as any,
    usingReRank: SEARCH_DEFAULT_RERANK,
    datasetSearchUsingExtensionQuery: false,
    datasetSearchExtensionModel: 'gpt-4o-mini',
    datasetSearchExtensionBg: ''
  }
}

/**
 * 获取沉思模式搜索选项
 */
export function getReflectionSearchOptions(): SearchOptions {
  return {
    limit: REFLECTION_SEARCH_LIMIT,
    similarity: REFLECTION_SEARCH_SIMILARITY,
    searchMode: REFLECTION_SEARCH_MODE as any,
    usingReRank: REFLECTION_SEARCH_RERANK,
    datasetSearchUsingExtensionQuery: REFLECTION_SEARCH_EXTENSION_QUERY,
    datasetSearchExtensionModel: REFLECTION_SEARCH_EXTENSION_MODEL,
    datasetSearchExtensionBg: REFLECTION_SEARCH_EXTENSION_BG
  }
}

/**
 * Push a single note to FastGPT knowledge base as a plain-text collection.
 * Returns the new collectionId on success.
 */
export async function syncNoteToFastGPT(note: SavedNote) {
  if (!BASE_URL || !API_KEY || !DATASET_ID) return null

  try {
    const res = await fetch(`${BASE_URL}/api/core/dataset/collection/create/text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        text: note.integratedNotes || note.structuredNotes || note.originalContent || '',
        datasetId: DATASET_ID,
        parentId: null,
        name: (note.title?.slice(0, COLLECTION_NAME_MAX_LENGTH) || 'Untitled').trim(),
        trainingType: SYNC_TRAINING_TYPE,
        chunkSettingMode: SYNC_CHUNK_SETTING_MODE,
        metadata: {
          sourceId: note.id,
          sourceType: note.sourceType || 'text',
          createdAt: new Date().toISOString(),
          syncedBy: SYNC_METADATA_SOURCE
        }
      })
    })

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`)
    }

    const json = (await res.json()) as FastGPTResponse<{ collectionId: string }>
    if (json.code !== 200) {
      throw new Error(json.message || 'FastGPT error')
    }
    return json.data.collectionId
  } catch (err) {
    console.error('[FastGPT] sync note failed:', err)
    return null
  }
}

/**
 * Delete a collection from FastGPT knowledge base.
 */
export async function deleteFromFastGPT(collectionId: string): Promise<boolean> {
  if (!BASE_URL || !API_KEY || !collectionId) return false

  try {
    // 使用URL参数而不是request body
    const res = await fetch(`${BASE_URL}/api/core/dataset/collection/delete?id=${collectionId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`
      }
    })

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`)
    }

    const json = (await res.json()) as FastGPTResponse<any>
    return json.code === 200
  } catch (err) {
    console.error('[FastGPT] delete collection failed:', err)
    return false
  }
}

/**
 * Get training status of a specific collection.
 */
export async function getCollectionStatus(collectionId: string): Promise<CollectionInfo | null> {
  if (!BASE_URL || !API_KEY || !collectionId) return null

  try {
    const res = await fetch(`${BASE_URL}/api/core/dataset/collection/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        datasetId: DATASET_ID,
        parentId: null,
        searchText: '',
        limit: COLLECTION_LIST_LIMIT
      })
    })

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`)
    }

    const json = (await res.json()) as FastGPTResponse<{ collections: CollectionInfo[] }>
    if (json.code !== 200) {
      throw new Error(json.message || 'FastGPT error')
    }

    return json.data.collections.find(c => c._id === collectionId) || null
  } catch (err) {
    console.error('[FastGPT] get collection status failed:', err)
    return null
  }
}

/**
 * List all collections in the dataset for sync management.
 */
export async function listAllCollections(): Promise<CollectionInfo[]> {
  if (!BASE_URL || !API_KEY || !DATASET_ID) return []

  try {
    const res = await fetch(`${BASE_URL}/api/core/dataset/collection/list`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`
      },
      body: JSON.stringify({
        datasetId: DATASET_ID,
        parentId: null,
        searchText: '',
        limit: COLLECTION_LIST_LIMIT
      })
    })

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`)
    }

    const json = (await res.json()) as FastGPTResponse<{ collections: CollectionInfo[] }>
    if (json.code !== 200) {
      throw new Error(json.message || 'FastGPT error')
    }

    return json.data.collections || []
  } catch (err) {
    console.error('[FastGPT] list collections failed:', err)
    return []
  }
}

/**
 * Search in FastGPT knowledge base with enhanced options.
 * Returns raw list of matched chunks with scoring.
 */
export async function searchKnowledgeBase(query: string, options: SearchOptions = {}): Promise<SearchResult[]> {
  if (!BASE_URL || !API_KEY || !DATASET_ID) return []

  try {
    const searchPayload = {
      datasetId: DATASET_ID,
      text: query,
      limit: Math.min(options.limit ?? SEARCH_DEFAULT_LIMIT, 20000),  // 确保不超过最大限制
      similarity: Math.max(0, Math.min(options.similarity ?? SEARCH_DEFAULT_SIMILARITY, 1)), // 确保在0-1范围内
      searchMode: options.searchMode ?? SEARCH_DEFAULT_MODE,
      usingReRank: options.usingReRank ?? SEARCH_DEFAULT_RERANK,
      datasetSearchUsingExtensionQuery: options.datasetSearchUsingExtensionQuery ?? false,
      datasetSearchExtensionModel: options.datasetSearchExtensionModel ?? 'gpt-4o-mini',
      datasetSearchExtensionBg: options.datasetSearchExtensionBg ?? ''
    }

    const res = await fetch(`${BASE_URL}/api/core/dataset/searchTest`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${API_KEY}`
      },
      body: JSON.stringify(searchPayload)
    })

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}`)
    }

    const json = (await res.json()) as FastGPTResponse<{list: SearchResult[]}>
    if (json.code !== 200) {
      throw new Error(json.message || 'FastGPT error')
    }

    return json.data.list || []
  } catch (err) {
    console.error('[FastGPT] search failed:', err)
    return []
  }
}

/**
 * 格式化搜索结果用于显示
 */
export function formatSearchResults(results: SearchResult[], maxResults: number = SEARCH_MAX_RESULTS, showScore: boolean = SEARCH_SHOW_SCORE): string {
  if (!results || results.length === 0) return ''
  
  return results
    .slice(0, maxResults)
    .map((r: SearchResult, idx: number) => {
      const content = r.q || r.a || ''
      let score = ''
      if (showScore && r.score) {
        // 处理不同格式的分数
        if (Array.isArray(r.score) && r.score.length > 0) {
          const scoreValue = r.score[0].value || 0
          score = ` (相关度: ${(scoreValue * 100).toFixed(1)}%)`
        } else if (typeof r.score === 'number') {
          score = ` (相关度: ${(r.score * 100).toFixed(1)}%)`
        }
      }
      return `【知识片段${idx + 1}${score}】${content}`
    })
    .join('\n\n')
} 