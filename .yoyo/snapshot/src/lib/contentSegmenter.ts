import { TextSegment } from './store'
import { createHash } from 'crypto'

/**
 * 内容分段处理器
 * 负责将原文内容分割成可定位的文本片段
 */
export class ContentSegmenter {
  private static instance: ContentSegmenter
  
  static getInstance(): ContentSegmenter {
    if (!ContentSegmenter.instance) {
      ContentSegmenter.instance = new ContentSegmenter()
    }
    return ContentSegmenter.instance
  }

  /**
   * 将文本内容分割成段落级别的片段
   */
  segmentText(content: string): TextSegment[] {
    const segments: TextSegment[] = []
    let currentOffset = 0
    
    // 按段落分割
    const paragraphs = content.split(/\n\s*\n/).filter(p => p.trim())
    
    for (let i = 0; i < paragraphs.length; i++) {
      const paragraph = paragraphs[i].trim()
      if (!paragraph) continue
      
      const startOffset = content.indexOf(paragraph, currentOffset)
      const endOffset = startOffset + paragraph.length
      
      const segment: TextSegment = {
        id: this.generateSegmentId(paragraph, i),
        content: paragraph,
        startOffset,
        endOffset,
        type: this.detectSegmentType(paragraph),
        hash: this.generateContentHash(paragraph),
        metadata: {
          parentId: i > 0 ? segments[i - 1]?.id : undefined
        }
      }
      
      segments.push(segment)
      currentOffset = endOffset
    }
    
    return segments
  }

  /**
   * 将HTML内容分割成带DOM路径的片段
   */
  segmentHTML(htmlContent: string): TextSegment[] {
    const segments: TextSegment[] = []
    
    // 创建临时DOM解析
    if (typeof window !== 'undefined') {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlContent, 'text/html')
      
      this.traverseDOM(doc.body, segments, '')
    } else {
      // 服务端回退到文本分割
      return this.segmentText(this.stripHTML(htmlContent))
    }
    
    return segments
  }

  /**
   * 遍历DOM节点并创建片段
   */
  private traverseDOM(node: Node, segments: TextSegment[], path: string) {
    if (node.nodeType === Node.TEXT_NODE) {
      const content = node.textContent?.trim()
      if (content && content.length > 10) { // 忽略过短的文本
        const segment: TextSegment = {
          id: this.generateSegmentId(content, segments.length),
          content,
          startOffset: 0, // 在HTML中需要重新计算
          endOffset: content.length,
          type: this.detectSegmentTypeFromDOM(node.parentElement),
          domPath: path,
          hash: this.generateContentHash(content)
        }
        segments.push(segment)
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element
      const newPath = path ? `${path} > ${element.tagName.toLowerCase()}` : element.tagName.toLowerCase()
      
      for (let i = 0; i < node.childNodes.length; i++) {
        this.traverseDOM(node.childNodes[i], segments, `${newPath}:nth-child(${i + 1})`)
      }
    }
  }

  /**
   * 检测片段类型
   */
  private detectSegmentType(content: string): TextSegment['type'] {
    if (content.match(/^#{1,6}\s/)) return 'heading'
    if (content.match(/^[\d\-\*\+]\s/)) return 'list-item'
    return 'paragraph'
  }

  /**
   * 从DOM元素检测片段类型
   */
  private detectSegmentTypeFromDOM(element: Element | null): TextSegment['type'] {
    if (!element) return 'paragraph'
    
    const tagName = element.tagName.toLowerCase()
    if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(tagName)) return 'heading'
    if (['li'].includes(tagName)) return 'list-item'
    return 'paragraph'
  }

  /**
   * 生成片段唯一ID
   */
  private generateSegmentId(content: string, index: number): string {
    const hash = this.generateContentHash(content)
    return `seg_${index}_${hash.substring(0, 8)}`
  }

  /**
   * 生成内容哈希
   */
  private generateContentHash(content: string): string {
    // 在浏览器环境中使用简单哈希
    if (typeof window !== 'undefined') {
      let hash = 0
      for (let i = 0; i < content.length; i++) {
        const char = content.charCodeAt(i)
        hash = ((hash << 5) - hash) + char
        hash = hash & hash // 转换为32位整数
      }
      return Math.abs(hash).toString(16)
    }
    
    // Node.js环境中使用crypto
    return createHash('md5').update(content).digest('hex').substring(0, 16)
  }

  /**
   * 移除HTML标签
   */
  private stripHTML(html: string): string {
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim()
  }

  /**
   * 根据内容变化更新片段映射
   */
  updateSegments(oldSegments: TextSegment[], newContent: string): {
    segments: TextSegment[]
    changes: {
      added: TextSegment[]
      modified: Array<{ old: TextSegment, new: TextSegment }>
      deleted: TextSegment[]
    }
  } {
    const newSegments = this.segmentText(newContent)
    const changes = {
      added: [] as TextSegment[],
      modified: [] as Array<{ old: TextSegment, new: TextSegment }>,
      deleted: [] as TextSegment[]
    }

    // 创建哈希映射以快速查找
    const oldHashMap = new Map(oldSegments.map(seg => [seg.hash, seg]))
    const newHashMap = new Map(newSegments.map(seg => [seg.hash, seg]))

    // 查找删除的片段
    for (const oldSeg of oldSegments) {
      if (!newHashMap.has(oldSeg.hash)) {
        changes.deleted.push(oldSeg)
      }
    }

    // 查找新增和修改的片段
    for (const newSeg of newSegments) {
      const oldSeg = oldHashMap.get(newSeg.hash)
      if (!oldSeg) {
        changes.added.push(newSeg)
      } else if (oldSeg.startOffset !== newSeg.startOffset || oldSeg.endOffset !== newSeg.endOffset) {
        changes.modified.push({ old: oldSeg, new: newSeg })
      }
    }

    return { segments: newSegments, changes }
  }
}

export const contentSegmenter = ContentSegmenter.getInstance()
