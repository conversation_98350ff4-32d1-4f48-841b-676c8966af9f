/**
 * 内容类型检测工具
 * 用于识别用户输入的内容类型（URL、文本等）
 */

// URL检测正则表达式
const URL_REGEX = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/

/**
 * 内容类型枚举
 */
export enum ContentType {
  URL = 'url',
  TEXT = 'text'
}

/**
 * 内容检测结果接口
 */
export interface ContentDetectionResult {
  type: ContentType
  originalInput: string
  isValid: boolean
  metadata?: {
    domain?: string
    protocol?: string
    textLength?: number
  }
}

/**
 * 检测内容类型
 * @param input 用户输入的内容
 * @returns 检测结果
 */
export function detectContentType(input: string): ContentDetectionResult {
  const trimmedInput = input.trim()
  
  // 检测是否为URL
  if (URL_REGEX.test(trimmedInput)) {
    try {
      const url = new URL(trimmedInput)
      return {
        type: ContentType.URL,
        originalInput: input,
        isValid: true,
        metadata: {
          domain: url.hostname,
          protocol: url.protocol
        }
      }
    } catch (error) {
      // URL格式错误
      return {
        type: ContentType.URL,
        originalInput: input,
        isValid: false
      }
    }
  }
  
  // 默认为文本内容
  return {
    type: ContentType.TEXT,
    originalInput: input,
    isValid: trimmedInput.length > 0,
    metadata: {
      textLength: trimmedInput.length
    }
  }
}

/**
 * 检查是否为有效的URL
 * @param input 输入内容
 * @returns 是否为有效URL
 */
export function isValidUrl(input: string): boolean {
  const result = detectContentType(input)
  return result.type === ContentType.URL && result.isValid
}

/**
 * 检查是否为文本内容
 * @param input 输入内容
 * @returns 是否为文本内容
 */
export function isTextContent(input: string): boolean {
  const result = detectContentType(input)
  return result.type === ContentType.TEXT && result.isValid
}

/**
 * 从URL提取域名
 * @param url URL字符串
 * @returns 域名或null
 */
export function extractDomain(url: string): string | null {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch (error) {
    return null
  }
}

/**
 * 生成内容摘要（用于标题生成）
 * @param content 文本内容
 * @param maxLength 最大长度
 * @returns 内容摘要
 */
export function generateContentSummary(content: string, maxLength: number = 200): string {
  const trimmed = content.trim()
  if (trimmed.length <= maxLength) {
    return trimmed
  }
  
  // 尝试在句号处截断
  const sentences = trimmed.split(/[。！？.!?]/)
  let summary = ''
  
  for (const sentence of sentences) {
    if ((summary + sentence).length <= maxLength) {
      summary += sentence + '。'
    } else {
      break
    }
  }
  
  // 如果没有找到合适的句号截断点，直接截断
  if (summary.length === 0) {
    summary = trimmed.substring(0, maxLength) + '...'
  }
  
  return summary
}
