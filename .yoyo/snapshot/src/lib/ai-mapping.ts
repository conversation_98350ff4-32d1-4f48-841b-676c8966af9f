import OpenAI from 'openai'
import { TextSegment, NoteReference } from '@/lib/store'

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY || '',
})

interface MappingResult {
  aiNote: string
  noteReferences: NoteReference[]
}

export async function generateStructuredNoteWithMapping(
  originalContent: string,
  textSegments: TextSegment[],
  title: string
): Promise<MappingResult> {
  try {
    // 为每个段落创建带ID的引用文本
    const segmentedContent = textSegments.map((seg, idx) => 
      `[段落${idx}:${seg.id}] ${seg.content}`
    ).join('\n\n')

    const systemPrompt = `你是一个专业的知识提炼助手。你的任务是：
1. 将用户提供的内容转换为结构化的知识笔记
2. 笔记必须包含以下部分：
   - # 摘要（50-100字的核心概述）
   - # 大纲（层级化的内容结构）
   - # 核心要点（3-5个关键信息点）
   - # 见解与思考（深度分析和个人见解）
3. 在生成笔记时，如果引用了原文中的内容，请在引用后添加来源标记，格式为：[来源:段落ID]
4. 确保每个要点都有对应的原文支撑
5. 使用Markdown格式，保持结构清晰`

    const userPrompt = `标题：${title}

原文内容（已分段标记）：
${segmentedContent}

请生成结构化笔记，并在适当位置标注引用来源。`

    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.7,
      max_tokens: 2000,
    })

    const aiNote = completion.choices[0]?.message?.content || ''

    // 解析AI笔记中的引用关系
    const noteReferences = parseNoteReferences(aiNote, textSegments)

    return {
      aiNote: cleanupSourceMarkers(aiNote), // 清理掉原始标记，保持笔记整洁
      noteReferences
    }
  } catch (error) {
    console.error('Error generating structured note with mapping:', error)
    throw error
  }
}

// 解析笔记中的引用关系
function parseNoteReferences(aiNote: string, textSegments: TextSegment[]): NoteReference[] {
  const references: NoteReference[] = []
  const lines = aiNote.split('\n')
  
  lines.forEach((line, lineIndex) => {
    // 查找形如 [来源:seg-xxx] 的标记
    const sourcePattern = /\[来源:(seg-\d+-\d+)\]/g
    let match
    
    while ((match = sourcePattern.exec(line)) !== null) {
      const segmentId = match[1]
      const segment = textSegments.find(s => s.id === segmentId)
      
      if (segment) {
        // 提取引用前的文本作为笔记内容
        const startIndex = Math.max(0, match.index - 100)
        const noteContent = line.substring(startIndex, match.index).trim()
        
        if (noteContent) {
          references.push({
            noteId: `ref-${lineIndex}-${match.index}`,
            noteContent,
            sourceSegments: [segmentId],
            confidence: 0.9,
            mappingType: 'exact',
            position: {
              startLine: lineIndex,
              endLine: lineIndex,
              startChar: startIndex,
              endChar: match.index
            }
          })
        }
      }
    }
  })

  // 如果没有找到明确的引用标记，尝试语义匹配
  if (references.length === 0) {
    references.push(...generateSemanticReferences(aiNote, textSegments))
  }

  return references
}

// 生成语义引用（当AI没有明确标记时的备用方案）
function generateSemanticReferences(aiNote: string, textSegments: TextSegment[]): NoteReference[] {
  const references: NoteReference[] = []
  const noteSections = aiNote.split('\n\n').filter(s => s.trim() && !s.startsWith('#'))
  
  noteSections.forEach((section, sectionIndex) => {
    // 简单的关键词匹配
    const matchedSegments = textSegments.filter(seg => {
      const keywords = extractKeywords(section)
      return keywords.some(keyword => 
        seg.content.toLowerCase().includes(keyword.toLowerCase())
      )
    })

    if (matchedSegments.length > 0) {
      references.push({
        noteId: `semantic-ref-${sectionIndex}`,
        noteContent: section.substring(0, 200),
        sourceSegments: matchedSegments.slice(0, 3).map(s => s.id), // 最多3个相关段落
        confidence: 0.6,
        mappingType: 'semantic',
        position: {
          startLine: sectionIndex * 3, // 估算位置
          endLine: sectionIndex * 3 + 2,
          startChar: 0,
          endChar: section.length
        }
      })
    }
  })

  return references
}

// 提取关键词（简单实现）
function extractKeywords(text: string): string[] {
  // 移除常见停用词，提取长度大于3的词
  const stopWords = new Set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '这', '个', '们', '到', '大', '地', '为', '子', '中', '你', '说', '生', '国', '年', '着', '就', '那', '后', '能', '对', '会', '他', '多', '然', '于', '自', '之'])
  
  const words = text.split(/[\s,，。.!！?？;；:：、]+/)
    .filter(word => word.length > 2)
    .filter(word => !stopWords.has(word))
    .slice(0, 5) // 取前5个关键词
  
  return words
}

// 清理笔记中的源标记
function cleanupSourceMarkers(aiNote: string): string {
  return aiNote.replace(/\[来源:seg-\d+-\d+\]/g, '')
}