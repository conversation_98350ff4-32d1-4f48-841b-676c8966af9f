import { NoteReference, TextSegment } from './store'

/**
 * 双向导航器
 * 处理笔记与原文之间的双向定位和导航
 */

export interface NavigationState {
  activeSegmentIds: string[]
  activeNoteIds: string[]
  highlightedElements: string[]
  scrollTarget: {
    type: 'segment' | 'note'
    id: string
  } | null
}

export interface NavigationCallbacks {
  onSegmentHighlight?: (segmentIds: string[]) => void
  onNoteHighlight?: (noteIds: string[]) => void
  onScrollToSegment?: (segmentId: string) => void
  onScrollToNote?: (noteId: string) => void
  onShowTooltip?: (content: string, position: { x: number, y: number }) => void
  onHideTooltip?: () => void
}

export class BidirectionalNavigator {
  private navigationState: NavigationState = {
    activeSegmentIds: [],
    activeNoteIds: [],
    highlightedElements: [],
    scrollTarget: null
  }

  private callbacks: NavigationCallbacks = {}
  private noteReferences: NoteReference[] = []
  private textSegments: TextSegment[] = []

  // 创建映射索引
  private segmentToNotesMap = new Map<string, NoteReference[]>()
  private noteToSegmentsMap = new Map<string, string[]>()

  constructor(callbacks: NavigationCallbacks = {}) {
    this.callbacks = callbacks
  }

  /**
   * 初始化导航器
   */
  initialize(noteReferences: NoteReference[], textSegments: TextSegment[]) {
    this.noteReferences = noteReferences
    this.textSegments = textSegments
    this.buildMappingIndexes()
  }

  /**
   * 构建映射索引
   */
  private buildMappingIndexes() {
    this.segmentToNotesMap.clear()
    this.noteToSegmentsMap.clear()

    for (const noteRef of this.noteReferences) {
      // 构建片段到笔记的映射
      for (const segmentId of noteRef.sourceSegments) {
        if (!this.segmentToNotesMap.has(segmentId)) {
          this.segmentToNotesMap.set(segmentId, [])
        }
        this.segmentToNotesMap.get(segmentId)!.push(noteRef)
      }

      // 构建笔记到片段的映射
      this.noteToSegmentsMap.set(noteRef.noteId, noteRef.sourceSegments)
    }
  }

  /**
   * 处理原文片段点击
   */
  handleSegmentClick(segmentId: string, event?: MouseEvent) {
    const relatedNotes = this.segmentToNotesMap.get(segmentId) || []
    
    if (relatedNotes.length === 0) {
      console.warn(`片段 ${segmentId} 没有关联的笔记`)
      return
    }

    // 更新导航状态
    this.navigationState.activeSegmentIds = [segmentId]
    this.navigationState.activeNoteIds = relatedNotes.map(note => note.noteId)
    this.navigationState.scrollTarget = { type: 'note', id: relatedNotes[0].noteId }

    // 触发回调
    this.callbacks.onSegmentHighlight?.(this.navigationState.activeSegmentIds)
    this.callbacks.onNoteHighlight?.(this.navigationState.activeNoteIds)
    this.callbacks.onScrollToNote?.(relatedNotes[0].noteId)

    // 显示提示信息
    if (event && this.callbacks.onShowTooltip) {
      const tooltipContent = this.generateSegmentTooltip(segmentId, relatedNotes)
      this.callbacks.onShowTooltip(tooltipContent, { x: event.clientX, y: event.clientY })
    }

    console.log('片段点击:', segmentId, '关联笔记:', relatedNotes.map(n => n.noteId))
  }

  /**
   * 处理笔记内容点击
   */
  handleNoteClick(noteId: string, event?: MouseEvent) {
    const relatedSegments = this.noteToSegmentsMap.get(noteId) || []
    
    if (relatedSegments.length === 0) {
      console.warn(`笔记 ${noteId} 没有关联的原文片段`)
      return
    }

    // 更新导航状态
    this.navigationState.activeNoteIds = [noteId]
    this.navigationState.activeSegmentIds = relatedSegments
    this.navigationState.scrollTarget = { type: 'segment', id: relatedSegments[0] }

    // 触发回调
    this.callbacks.onNoteHighlight?.(this.navigationState.activeNoteIds)
    this.callbacks.onSegmentHighlight?.(this.navigationState.activeSegmentIds)
    this.callbacks.onScrollToSegment?.(relatedSegments[0])

    // 显示提示信息
    if (event && this.callbacks.onShowTooltip) {
      const noteRef = this.noteReferences.find(ref => ref.noteId === noteId)
      if (noteRef) {
        const tooltipContent = this.generateNoteTooltip(noteRef, relatedSegments)
        this.callbacks.onShowTooltip(tooltipContent, { x: event.clientX, y: event.clientY })
      }
    }

    console.log('笔记点击:', noteId, '关联片段:', relatedSegments)
  }

  /**
   * 处理悬停事件
   */
  handleSegmentHover(segmentId: string | null, event?: MouseEvent) {
    if (!segmentId) {
      this.clearHover()
      return
    }

    const relatedNotes = this.segmentToNotesMap.get(segmentId) || []
    
    // 临时高亮
    this.callbacks.onNoteHighlight?.(relatedNotes.map(note => note.noteId))

    // 显示悬停提示
    if (event && this.callbacks.onShowTooltip && relatedNotes.length > 0) {
      const tooltipContent = this.generateSegmentTooltip(segmentId, relatedNotes)
      this.callbacks.onShowTooltip(tooltipContent, { x: event.clientX, y: event.clientY })
    }
  }

  /**
   * 处理笔记悬停事件
   */
  handleNoteHover(noteId: string | null, event?: MouseEvent) {
    if (!noteId) {
      this.clearHover()
      return
    }

    const relatedSegments = this.noteToSegmentsMap.get(noteId) || []
    
    // 临时高亮
    this.callbacks.onSegmentHighlight?.(relatedSegments)

    // 显示悬停提示
    if (event && this.callbacks.onShowTooltip && relatedSegments.length > 0) {
      const noteRef = this.noteReferences.find(ref => ref.noteId === noteId)
      if (noteRef) {
        const tooltipContent = this.generateNoteTooltip(noteRef, relatedSegments)
        this.callbacks.onShowTooltip(tooltipContent, { x: event.clientX, y: event.clientY })
      }
    }
  }

  /**
   * 清除悬停状态
   */
  clearHover() {
    this.callbacks.onHideTooltip?.()
    // 恢复到点击状态的高亮
    this.callbacks.onSegmentHighlight?.(this.navigationState.activeSegmentIds)
    this.callbacks.onNoteHighlight?.(this.navigationState.activeNoteIds)
  }

  /**
   * 清除所有高亮
   */
  clearAllHighlights() {
    this.navigationState = {
      activeSegmentIds: [],
      activeNoteIds: [],
      highlightedElements: [],
      scrollTarget: null
    }

    this.callbacks.onSegmentHighlight?.([])
    this.callbacks.onNoteHighlight?.([])
    this.callbacks.onHideTooltip?.()
  }

  /**
   * 生成片段提示内容
   */
  private generateSegmentTooltip(segmentId: string, relatedNotes: NoteReference[]): string {
    const segment = this.textSegments.find(seg => seg.id === segmentId)
    if (!segment) return ''

    return `
      <div class="text-sm">
        <div class="font-semibold mb-2">原文片段</div>
        <div class="text-gray-600 mb-2">${segment.content.substring(0, 100)}...</div>
        <div class="border-t pt-2">
          <div class="font-medium">关联笔记 (${relatedNotes.length})</div>
          ${relatedNotes.slice(0, 3).map(note => 
            `<div class="text-xs text-blue-600">• ${note.noteContent.substring(0, 50)}...</div>`
          ).join('')}
          ${relatedNotes.length > 3 ? `<div class="text-xs text-gray-500">还有 ${relatedNotes.length - 3} 条...</div>` : ''}
        </div>
      </div>
    `
  }

  /**
   * 生成笔记提示内容
   */
  private generateNoteTooltip(noteRef: NoteReference, relatedSegments: string[]): string {
    return `
      <div class="text-sm">
        <div class="font-semibold mb-2">笔记内容</div>
        <div class="text-gray-600 mb-2">${noteRef.noteContent}</div>
        <div class="border-t pt-2">
          <div class="font-medium">定位信息</div>
          <div class="text-xs text-gray-500">
            关联片段: ${relatedSegments.length} 个<br/>
            置信度: ${Math.round(noteRef.confidence * 100)}%<br/>
            类型: ${noteRef.mappingType}
          </div>
        </div>
      </div>
    `
  }

  /**
   * 获取当前导航状态
   */
  getNavigationState(): NavigationState {
    return { ...this.navigationState }
  }

  /**
   * 获取片段的关联笔记
   */
  getRelatedNotes(segmentId: string): NoteReference[] {
    return this.segmentToNotesMap.get(segmentId) || []
  }

  /**
   * 获取笔记的关联片段
   */
  getRelatedSegments(noteId: string): string[] {
    return this.noteToSegmentsMap.get(noteId) || []
  }

  /**
   * 检查片段是否有关联笔记
   */
  hasRelatedNotes(segmentId: string): boolean {
    return this.segmentToNotesMap.has(segmentId)
  }

  /**
   * 检查笔记是否有关联片段
   */
  hasRelatedSegments(noteId: string): boolean {
    return this.noteToSegmentsMap.has(noteId)
  }

  /**
   * 更新回调函数
   */
  updateCallbacks(callbacks: Partial<NavigationCallbacks>) {
    this.callbacks = { ...this.callbacks, ...callbacks }
  }

  /**
   * 获取统计信息
   */
  getStatistics() {
    return {
      totalNoteReferences: this.noteReferences.length,
      totalTextSegments: this.textSegments.length,
      mappedSegments: this.segmentToNotesMap.size,
      mappedNotes: this.noteToSegmentsMap.size,
      averageConfidence: this.noteReferences.reduce((sum, ref) => sum + ref.confidence, 0) / this.noteReferences.length || 0
    }
  }
}

export const createBidirectionalNavigator = (callbacks?: NavigationCallbacks) => {
  return new BidirectionalNavigator(callbacks)
}
