import { TextSegment, NoteReference } from './store'
import OpenAI from 'openai'

/**
 * 智能笔记映射生成器
 * 使用AI分析笔记内容与原文的关联关系
 */
export class NoteMappingGenerator {
  private openai: OpenAI

  constructor() {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    })
  }

  /**
   * 生成笔记与原文的映射关系
   */
  async generateMappings(
    originalContent: string,
    structuredNotes: string,
    textSegments: TextSegment[]
  ): Promise<NoteReference[]> {
    try {
      // 将结构化笔记分解为独立的笔记片段
      const noteSegments = this.parseNoteSegments(structuredNotes)
      const mappings: NoteReference[] = []

      for (const noteSegment of noteSegments) {
        const mapping = await this.generateSingleMapping(
          noteSegment,
          textSegments,
          originalContent
        )
        if (mapping) {
          mappings.push(mapping)
        }
      }

      return mappings
    } catch (error) {
      console.error('生成映射关系失败:', error)
      return []
    }
  }

  /**
   * 解析结构化笔记为独立片段
   */
  private parseNoteSegments(structuredNotes: string): Array<{ id: string, content: string, type: string }> {
    const segments: Array<{ id: string, content: string, type: string }> = []
    const lines = structuredNotes.split('\n')
    let currentSegment = ''
    let currentType = 'paragraph'
    let segmentIndex = 0

    for (const line of lines) {
      const trimmedLine = line.trim()
      
      if (trimmedLine.startsWith('#')) {
        // 保存前一个片段
        if (currentSegment.trim()) {
          segments.push({
            id: `note_${segmentIndex++}`,
            content: currentSegment.trim(),
            type: currentType
          })
        }
        
        // 开始新的标题片段
        currentSegment = trimmedLine
        currentType = 'heading'
      } else if (trimmedLine.startsWith('-') || trimmedLine.startsWith('*')) {
        // 列表项
        if (currentType !== 'list') {
          // 保存前一个片段
          if (currentSegment.trim()) {
            segments.push({
              id: `note_${segmentIndex++}`,
              content: currentSegment.trim(),
              type: currentType
            })
          }
          currentSegment = trimmedLine
          currentType = 'list'
        } else {
          currentSegment += '\n' + trimmedLine
        }
      } else if (trimmedLine) {
        // 普通段落
        if (currentType === 'list' || currentType === 'heading') {
          // 保存前一个片段
          if (currentSegment.trim()) {
            segments.push({
              id: `note_${segmentIndex++}`,
              content: currentSegment.trim(),
              type: currentType
            })
          }
          currentSegment = trimmedLine
          currentType = 'paragraph'
        } else {
          currentSegment += (currentSegment ? '\n' : '') + trimmedLine
        }
      }
    }

    // 保存最后一个片段
    if (currentSegment.trim()) {
      segments.push({
        id: `note_${segmentIndex++}`,
        content: currentSegment.trim(),
        type: currentType
      })
    }

    return segments
  }

  /**
   * 为单个笔记片段生成映射
   */
  private async generateSingleMapping(
    noteSegment: { id: string, content: string, type: string },
    textSegments: TextSegment[],
    originalContent: string
  ): Promise<NoteReference | null> {
    try {
      // 首先尝试精确匹配
      const exactMatches = this.findExactMatches(noteSegment.content, textSegments)
      if (exactMatches.length > 0) {
        return {
          noteId: noteSegment.id,
          noteContent: noteSegment.content,
          sourceSegments: exactMatches.map(seg => seg.id),
          confidence: 0.95,
          mappingType: 'exact',
          position: this.calculatePosition(exactMatches, originalContent)
        }
      }

      // 使用AI进行语义匹配
      const semanticMatches = await this.findSemanticMatches(
        noteSegment.content,
        textSegments,
        originalContent
      )

      if (semanticMatches.length > 0) {
        return {
          noteId: noteSegment.id,
          noteContent: noteSegment.content,
          sourceSegments: semanticMatches.map(seg => seg.id),
          confidence: 0.75,
          mappingType: 'semantic',
          position: this.calculatePosition(semanticMatches, originalContent)
        }
      }

      return null
    } catch (error) {
      console.error('生成单个映射失败:', error)
      return null
    }
  }

  /**
   * 查找精确匹配的文本片段
   */
  private findExactMatches(noteContent: string, textSegments: TextSegment[]): TextSegment[] {
    const matches: TextSegment[] = []
    const cleanNoteContent = this.cleanText(noteContent)

    for (const segment of textSegments) {
      const cleanSegmentContent = this.cleanText(segment.content)
      
      // 检查是否包含相同的关键短语
      if (this.hasSignificantOverlap(cleanNoteContent, cleanSegmentContent)) {
        matches.push(segment)
      }
    }

    return matches
  }

  /**
   * 使用AI进行语义匹配
   */
  private async findSemanticMatches(
    noteContent: string,
    textSegments: TextSegment[],
    originalContent: string
  ): Promise<TextSegment[]> {
    try {
      const prompt = `
请分析以下笔记内容与原文片段的关联关系，返回最相关的片段编号。

笔记内容：
${noteContent}

原文片段：
${textSegments.map((seg, index) => `[${index}] ${seg.content.substring(0, 200)}...`).join('\n\n')}

请返回JSON格式：
{
  "relevantSegments": [片段编号数组],
  "confidence": 置信度(0-1),
  "reasoning": "关联原因"
}
`

      const response = await this.openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的文本分析助手，擅长识别文本之间的语义关联关系。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.3,
        max_tokens: 500
      })

      const result = JSON.parse(response.choices[0]?.message?.content || '{}')
      const relevantSegments = result.relevantSegments || []
      
      return relevantSegments
        .filter((index: number) => index >= 0 && index < textSegments.length)
        .map((index: number) => textSegments[index])
    } catch (error) {
      console.error('AI语义匹配失败:', error)
      return []
    }
  }

  /**
   * 清理文本，移除格式化字符
   */
  private cleanText(text: string): string {
    return text
      .replace(/[#*\-\[\]]/g, '') // 移除Markdown格式字符
      .replace(/\s+/g, ' ') // 标准化空白字符
      .toLowerCase()
      .trim()
  }

  /**
   * 检查两个文本是否有显著重叠
   */
  private hasSignificantOverlap(text1: string, text2: string): boolean {
    const words1 = text1.split(/\s+/).filter(w => w.length > 3)
    const words2 = text2.split(/\s+/).filter(w => w.length > 3)
    
    const commonWords = words1.filter(word => words2.includes(word))
    const overlapRatio = commonWords.length / Math.min(words1.length, words2.length)
    
    return overlapRatio > 0.3 // 30%以上的词汇重叠
  }

  /**
   * 计算片段在原文中的位置
   */
  private calculatePosition(segments: TextSegment[], originalContent: string) {
    if (segments.length === 0) {
      return { startLine: 0, endLine: 0, startChar: 0, endChar: 0 }
    }

    const firstSegment = segments[0]
    const lastSegment = segments[segments.length - 1]
    
    // 计算行号
    const beforeText = originalContent.substring(0, firstSegment.startOffset)
    const startLine = beforeText.split('\n').length
    
    const endText = originalContent.substring(0, lastSegment.endOffset)
    const endLine = endText.split('\n').length

    return {
      startLine,
      endLine,
      startChar: firstSegment.startOffset,
      endChar: lastSegment.endOffset
    }
  }
}

export const noteMappingGenerator = new NoteMappingGenerator()
