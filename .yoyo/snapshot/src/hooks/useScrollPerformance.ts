import { useEffect, useRef, useState, useCallback } from 'react'
import { getScrollConfig } from '@/config/scrollConfig'

interface PerformanceMetrics {
  scrollEventCount: number
  avgScrollEventDuration: number
  maxScrollEventDuration: number
  lastScrollEventDuration: number
  isPerformanceGood: boolean
  frameDropCount: number
  memoryUsage?: number
}

interface UseScrollPerformanceOptions {
  enabled?: boolean
  sampleRate?: number // 采样率，0-1 之间
  reportInterval?: number // 报告间隔（毫秒）
}

/**
 * 滚动性能监控 Hook
 * 监控滚动事件的性能表现，自动调整滚动配置
 */
export function useScrollPerformance(options: UseScrollPerformanceOptions = {}) {
  const {
    enabled = true,
    sampleRate = 0.1, // 10% 采样率
    reportInterval = 10000 // 10秒报告一次
  } = options

  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    scrollEventCount: 0,
    avgScrollEventDuration: 0,
    maxScrollEventDuration: 0,
    lastScrollEventDuration: 0,
    isPerformanceGood: true,
    frameDropCount: 0
  })

  const metricsRef = useRef<{
    startTime: number
    scrollEventDurations: number[]
    frameTimestamps: number[]
    reportTimer?: NodeJS.Timeout
  }>({
    startTime: Date.now(),
    scrollEventDurations: [],
    frameTimestamps: []
  })

  const scrollConfig = getScrollConfig()

  // 测量滚动事件性能
  const measureScrollEvent = useCallback((callback: () => void) => {
    if (!enabled || Math.random() > sampleRate) {
      callback()
      return
    }

    const startTime = performance.now()
    
    callback()
    
    const endTime = performance.now()
    const duration = endTime - startTime

    metricsRef.current.scrollEventDurations.push(duration)
    
    // 保持最近 100 个样本
    if (metricsRef.current.scrollEventDurations.length > 100) {
      metricsRef.current.scrollEventDurations.shift()
    }

    // 更新指标
    const durations = metricsRef.current.scrollEventDurations
    const avgDuration = durations.reduce((sum, d) => sum + d, 0) / durations.length
    const maxDuration = Math.max(...durations)
    
    setMetrics(prev => ({
      ...prev,
      scrollEventCount: prev.scrollEventCount + 1,
      avgScrollEventDuration: avgDuration,
      maxScrollEventDuration: maxDuration,
      lastScrollEventDuration: duration,
      isPerformanceGood: avgDuration < 16 && maxDuration < 50 // 目标：平均<16ms，最大<50ms
    }))
  }, [enabled, sampleRate])

  // 检测帧丢失
  const checkFrameDrops = useCallback(() => {
    if (!enabled) return

    const now = performance.now()
    metricsRef.current.frameTimestamps.push(now)

    // 保持最近 60 个帧时间戳（约1秒）
    if (metricsRef.current.frameTimestamps.length > 60) {
      const timestamps = metricsRef.current.frameTimestamps
      let frameDrops = 0

      for (let i = 1; i < timestamps.length; i++) {
        const frameDuration = timestamps[i] - timestamps[i - 1]
        // 如果帧间隔超过 20ms（低于 50fps），认为是帧丢失
        if (frameDuration > 20) {
          frameDrops++
        }
      }

      setMetrics(prev => ({
        ...prev,
        frameDropCount: frameDrops
      }))

      metricsRef.current.frameTimestamps.shift()
    }

    requestAnimationFrame(checkFrameDrops)
  }, [enabled])

  // 获取内存使用情况
  const updateMemoryUsage = useCallback(() => {
    if (!enabled || !(performance as any).memory) return

    const memory = (performance as any).memory
    const memoryUsage = memory.usedJSHeapSize / memory.totalJSHeapSize

    setMetrics(prev => ({
      ...prev,
      memoryUsage
    }))
  }, [enabled])

  // 定期报告性能数据
  const reportPerformance = useCallback(() => {
    if (!enabled) return

    const { scrollEventCount, avgScrollEventDuration, maxScrollEventDuration, frameDropCount, memoryUsage } = metrics

    // 只在开发环境或者性能有问题时报告
    if (process.env.NODE_ENV === 'development' || !metrics.isPerformanceGood) {
      console.group('🔍 滚动性能报告')
      console.log('📊 滚动事件数量:', scrollEventCount)
      console.log('⏱️ 平均处理时间:', avgScrollEventDuration.toFixed(2) + 'ms')
      console.log('🔥 最大处理时间:', maxScrollEventDuration.toFixed(2) + 'ms')
      console.log('📉 帧丢失数量:', frameDropCount)
      if (memoryUsage) {
        console.log('💾 内存使用率:', (memoryUsage * 100).toFixed(1) + '%')
      }
      console.log('✅ 性能状态:', metrics.isPerformanceGood ? '良好' : '需要优化')
      console.groupEnd()
    }

    // 发送埋点数据
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', 'scroll_performance_report', {
        event_category: 'performance',
        scroll_event_count: scrollEventCount,
        avg_duration: Math.round(avgScrollEventDuration),
        max_duration: Math.round(maxScrollEventDuration),
        frame_drops: frameDropCount,
        memory_usage: memoryUsage ? Math.round(memoryUsage * 100) : undefined,
        is_performance_good: metrics.isPerformanceGood
      })
    }
  }, [enabled, metrics])

  // 获取优化建议
  const getOptimizationSuggestions = useCallback(() => {
    const suggestions: string[] = []

    if (metrics.avgScrollEventDuration > 16) {
      suggestions.push('考虑增加滚动事件节流间隔')
    }

    if (metrics.maxScrollEventDuration > 50) {
      suggestions.push('减少滚动事件处理的复杂度')
    }

    if (metrics.frameDropCount > 10) {
      suggestions.push('优化动画性能，减少重绘')
    }

    if (metrics.memoryUsage && metrics.memoryUsage > 0.8) {
      suggestions.push('检查内存泄漏，清理不必要的引用')
    }

    return suggestions
  }, [metrics])

  // 动态调整滚动配置
  const getAdaptiveScrollConfig = useCallback(() => {
    const baseConfig = scrollConfig
    
    if (!metrics.isPerformanceGood) {
      // 性能不佳时使用更保守的设置
      return {
        ...baseConfig,
        scrollFollow: {
          ...baseConfig.scrollFollow,
          throttleMs: Math.max(baseConfig.scrollFollow.throttleMs * 2, 32),
          debounceMs: Math.max(baseConfig.scrollFollow.debounceMs * 1.5, 300)
        },
        performance: {
          ...baseConfig.performance,
          useRaf: true,
          mutationDebounce: Math.max(baseConfig.performance.mutationDebounce * 2, 200)
        }
      }
    }

    return baseConfig
  }, [scrollConfig, metrics.isPerformanceGood])

  // 初始化性能监控
  useEffect(() => {
    if (!enabled) return

    // 启动帧丢失检测
    requestAnimationFrame(checkFrameDrops)

    // 定期更新内存使用情况
    const memoryTimer = setInterval(updateMemoryUsage, 1000)

    // 定期报告性能
    const reportTimer = setInterval(reportPerformance, reportInterval)
    metricsRef.current.reportTimer = reportTimer

    return () => {
      clearInterval(memoryTimer)
      clearInterval(reportTimer)
    }
  }, [enabled, checkFrameDrops, updateMemoryUsage, reportPerformance, reportInterval])

  return {
    metrics,
    measureScrollEvent,
    getOptimizationSuggestions,
    getAdaptiveScrollConfig,
    
    // 便捷方法
    isPerformanceGood: metrics.isPerformanceGood,
    shouldUseThrottling: metrics.avgScrollEventDuration > 10,
    shouldReduceAnimations: metrics.frameDropCount > 5
  }
}