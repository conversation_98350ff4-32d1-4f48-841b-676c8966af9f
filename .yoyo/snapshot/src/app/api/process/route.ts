import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'
import { scrapeWebPage } from '@/lib/scraper'
import { detectContentType, ContentType } from '@/lib/content-detector'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

// 生成结构化笔记
async function generateStructuredNote(content: string, title: string) {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回模拟的结构化笔记
    return `# ${title}

## 核心观点总结
- 这是一个演示用的结构化笔记
- 内容长度：${content.length} 字符
- 生成时间：${new Date().toLocaleString('zh-CN')}

## 关键信息提取
- **主要内容**：${content.substring(0, 100)}${content.length > 100 ? '...' : ''}
- **内容类型**：文本分析
- **处理状态**：演示模式

## 实用价值分析
- 📝 **知识价值**：中等
- 🎯 **实用性**：演示功能
- 💡 **启发性**：展示结构化笔记生成能力

> 💡 **提示**：这是演示模式生成的笔记。要获得真正的智能分析，请配置有效的OpenAI API密钥。`
  }

  try {
    const systemPrompt = process.env.AI_SYSTEM_PROMPT || `你是一个专业的知识提炼助手。请将用户提供的内容转换为结构化的笔记，包含：

## 📋 核心观点
- 提取3-5个最重要的核心观点
- 每个观点用简洁的语言表达

## 🔍 关键信息
- 重要的数据、事实、引用
- 关键人物、时间、地点
- 专业术语解释

## 💡 实用价值
- 这些信息的实际应用场景
- 对读者的启发和建议
- 可行动的要点

## 🎯 总结
- 一句话总结全文精髓

请用Markdown格式输出，保持简洁明了，使用合适的emoji和格式化。`

    const response = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: `请为以下内容生成结构化笔记：

**标题：** ${title}

**内容：**
${content}

请按照系统提示的格式生成结构化笔记，确保内容有层次感和可读性。`
        }
      ],
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000'),
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
    })

    return response.choices[0]?.message?.content || '无法生成结构化笔记'
  } catch (error) {
    console.error('生成结构化笔记失败:', error)
    return '抱歉，结构化笔记生成失败，请稍后重试。'
  }
}

// 智能标题生成函数
async function generateSmartTitle(input: string, contentType: ContentType, webTitle?: string): Promise<string> {
  if (contentType === ContentType.URL) {
    // 对于URL，使用网页标题或域名
    if (webTitle) {
      // 处理网页标题
      const cleanTitle = webTitle
        .replace(/\s*[-–—|]\s*.*$/, '') // 移除 - | 后的内容
        .replace(/\s*_.*$/, '') // 移除 _ 后的内容
        .replace(/\s*\(.*\)$/, '') // 移除括号内容
        .replace(/\s*【.*】$/, '') // 移除中文括号内容
        .trim()

      return cleanTitle.length > 10 ? cleanTitle.substring(0, 10) : cleanTitle || '网页内容'
    } else {
      // 如果没有网页标题，使用域名
      try {
        const url = new URL(input)
        const domain = url.hostname.replace('www.', '')
        return domain.length > 10 ? domain.substring(0, 10) : domain
      } catch (error) {
        return '网页链接'
      }
    }
  } else {
    // 对于文本内容，使用AI生成标题
    return await generateTitleForText(input)
  }
}

// 为文本内容生成简洁标题
async function generateTitleForText(content: string): Promise<string> {
  // 检查是否有有效的OpenAI API密钥
  const apiKey = process.env.OPENAI_API_KEY
  if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
    // 返回基于内容的简单标题
    const words = content.trim().split(/\s+/).slice(0, 5).join(' ')
    return words.length > 10 ? words.substring(0, 10) : words || '新内容'
  }

  try {
    // 使用全量内容进行标题生成（与结构化笔记生成相同的提交方式）
    const response = await openai.chat.completions.create({
      model: process.env.TITLE_GENERATION_MODEL || 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: process.env.TITLE_GENERATION_PROMPT || '你是一个专业的标题生成助手。请为用户提供的内容生成一个简洁、准确的标题。要求：1. 标题长度不超过10个字 2. 准确概括内容核心 3. 使用中文 4. 避免使用标点符号'
        },
        {
          role: 'user',
          content: `请为以下内容生成一个10个字以内的简洁标题：\n\n${content}`
        }
      ],
      max_tokens: parseInt(process.env.TITLE_MAX_TOKENS || '100'),
      temperature: parseFloat(process.env.TITLE_TEMPERATURE || '0.3')
    })

    const generatedTitle = response.choices[0]?.message?.content?.trim() || ''

    // 确保标题不超过10个字
    if (generatedTitle.length > 10) {
      return generatedTitle.substring(0, 10)
    }

    return generatedTitle || '新内容'
  } catch (error) {
    console.error('标题生成失败:', error)
    // 降级处理：使用内容前几个字作为标题
    const fallbackTitle = content.trim().substring(0, 10)
    return fallbackTitle || '新内容'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()

    if (!input) {
      return NextResponse.json(
        { error: '缺少输入内容' },
        { status: 400 }
      )
    }

    // 自动检测内容类型（如果未提供type参数）
    const detectionResult = type ?
      { type: type === 'url' ? ContentType.URL : ContentType.TEXT } :
      detectContentType(input)

    let title: string
    let content: string
    let textContent: string
    let webTitle: string | undefined

    if (detectionResult.type === ContentType.URL || type === 'url') {
      // 使用更健壮的网页抓取逻辑
      try {
        const urlData = await scrapeWebPage(input)
        webTitle = urlData.title
        content = urlData.content
        textContent = urlData.textContent

        // 使用智能标题生成
        title = await generateSmartTitle(input, ContentType.URL, webTitle)
      } catch (error) {
        console.error('网页抓取失败:', error)
        return NextResponse.json(
          {
            error: '无法获取网页内容。可能原因：1) 网站拒绝访问 2) 网络连接问题 3) 网页需要JavaScript渲染',
            success: false
          },
          { status: 500 }
        )
      }
    } else {
      // 处理文本内容
      content = input // 保持原始文本格式
      textContent = input

      // 使用智能标题生成
      title = await generateSmartTitle(input, ContentType.TEXT)
    }

    // 生成结构化笔记
    const aiNote = await generateStructuredNote(textContent, title)

    return NextResponse.json({
      title,
      content,
      aiNote,
      contentType: detectionResult.type,
      success: true
    })

  } catch (error) {
    console.error('处理请求失败:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : '处理失败',
        success: false
      },
      { status: 500 }
    )
  }
}
