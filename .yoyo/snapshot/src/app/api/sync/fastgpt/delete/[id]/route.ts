import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { deleteFromFastGPT } from '@/lib/fastgpt'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const noteId = params.id

    // 查找笔记
    const note = await prisma.savedNote.findUnique({
      where: { id: noteId }
    })

    if (!note) {
      return NextResponse.json(
        { error: 'Note not found' },
        { status: 404 }
      )
    }

    let fastgptDeleted = false

    // 如果有FastGPT的collectionId，先从FastGPT删除
    if (note.fastgptCollectionId) {
      fastgptDeleted = await deleteFromFastGPT(note.fastgptCollectionId)
    }

    // 从本地数据库删除
    await prisma.savedNote.delete({
      where: { id: noteId }
    })

    return NextResponse.json({
      success: true,
      localDeleted: true,
      fastgptDeleted,
      message: fastgptDeleted 
        ? 'Successfully deleted from both local and FastGPT'
        : 'Deleted from local, but FastGPT deletion failed or no collection ID'
    })

  } catch (error) {
    console.error('[Delete Note] Error:', error)
    return NextResponse.json(
      { error: 'Failed to delete note' },
      { status: 500 }
    )
  }
} 