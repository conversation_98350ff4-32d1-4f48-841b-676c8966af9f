import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: NextRequest) {
  try {
    const { content, aiNote } = await request.json()
    
    if (!content && !aiNote) {
      return NextResponse.json(
        { error: '缺少内容或AI笔记' },
        { status: 400 }
      )
    }
    
    // 检查是否有有效的OpenAI API密钥
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
      // 返回模拟的推荐问题
      const mockQuestions = [
        {
          id: 'mock-1',
          question: '这个内容的核心观点是什么？',
          category: 'understanding'
        },
        {
          id: 'mock-2', 
          question: '如何将这些知识应用到实际中？',
          category: 'application'
        },
        {
          id: 'mock-3',
          question: '还有哪些相关的概念值得深入？',
          category: 'exploration'
        }
      ]
      
      return NextResponse.json({
        questions: mockQuestions,
        success: true
      })
    }

    // 构建prompt
    const systemPrompt = process.env.QUESTIONS_GENERATION_PROMPT || `你是一个专业的问题生成助手。基于用户提供的内容和AI笔记，生成3个有价值的推荐问题，帮助用户更深入地理解和思考内容。

要求：
1. 生成3个问题，涵盖不同的思考角度
2. 问题要具体、有针对性，能够引发深入思考
3. 问题长度控制在15-25个字之间
4. 返回JSON格式，包含id、question、category字段
5. category可以是：understanding（理解）、application（应用）、exploration（探索）、analysis（分析）

返回格式示例：
[
  {
    "id": "q1",
    "question": "这个概念的核心原理是什么？",
    "category": "understanding"
  }
]`

    const userPrompt = `请基于以下内容生成3个推荐问题：

原文内容：
${content || '无'}

AI笔记：
${aiNote || '无'}

请生成JSON格式的推荐问题数组：`

    try {
      const response = await openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userPrompt
          }
        ],
        max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '500'),
        temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7')
      })

      const result = response.choices[0]?.message?.content
      if (!result) {
        throw new Error('No response from OpenAI')
      }

      // 尝试解析JSON响应
      try {
        const questions = JSON.parse(result)
        
        // 验证和清理问题数据
        const validQuestions = questions
          .filter((q: any) => q.question && typeof q.question === 'string')
          .slice(0, 3) // 确保最多3个问题
          .map((q: any, index: number) => ({
            id: q.id || `q${index + 1}`,
            question: q.question.trim(),
            category: q.category || 'understanding'
          }))

        return NextResponse.json({
          questions: validQuestions,
          success: true
        })
      } catch (parseError) {
        // 如果直接解析失败，尝试提取JSON部分
        const jsonMatch = result.match(/\[[\s\S]*\]/)
        if (jsonMatch) {
          const questions = JSON.parse(jsonMatch[0])
          const validQuestions = questions
            .filter((q: any) => q.question && typeof q.question === 'string')
            .slice(0, 3)
            .map((q: any, index: number) => ({
              id: q.id || `q${index + 1}`,
              question: q.question.trim(),
              category: q.category || 'understanding'
            }))

          return NextResponse.json({
            questions: validQuestions,
            success: true
          })
        }
        throw new Error('Failed to parse questions from AI response')
      }
    } catch (error) {
      console.error('Error generating questions:', error)
      
      // 降级处理：返回通用问题
      const fallbackQuestions = [
        {
          id: 'fallback-1',
          question: '这个内容的主要观点是什么？',
          category: 'understanding'
        },
        {
          id: 'fallback-2',
          question: '如何将这些知识应用到实践中？',
          category: 'application'
        },
        {
          id: 'fallback-3',
          question: '还有哪些相关话题值得探索？',
          category: 'exploration'
        }
      ]
      
      return NextResponse.json({
        questions: fallbackQuestions,
        success: true
      })
    }

  } catch (error) {
    console.error('Generate questions API error:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : '生成推荐问题失败',
        success: false
      },
      { status: 500 }
    )
  }
}
