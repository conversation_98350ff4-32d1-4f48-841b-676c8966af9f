import { NextRequest, NextResponse } from 'next/server'
import OpenAI from 'openai'

// 初始化OpenAI客户端
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
})

export async function POST(request: NextRequest) {
  try {
    const {
      originalContent,
      structuredNotes,
      chatMessages
    } = await request.json()

    // 验证必需字段
    if (!originalContent || !structuredNotes) {
      return NextResponse.json(
        { error: '缺少必需字段' },
        { status: 400 }
      )
    }

    // 检查是否有有效的OpenAI API密钥
    const apiKey = process.env.OPENAI_API_KEY
    if (!apiKey || apiKey === 'sk-test-key-for-development' || apiKey === 'your_openai_api_key_here') {
      // 返回模拟的整合结果
      const mockIntegratedNotes = `${structuredNotes}

## 对话要点整合

${chatMessages && chatMessages.length > 0 ? 
  chatMessages.filter((msg: any) => msg.role === 'user')
    .map((msg: any, index: number) => `${index + 1}. ${msg.content}`)
    .join('\n') : 
  '暂无对话内容'}

---
*本笔记已整合对话内容，便于后续回顾和学习。*`

      return NextResponse.json({
        integratedNotes: mockIntegratedNotes
      })
    }

    // 构建整合提示词
    const systemPrompt = `你是一个专业的知识整理助手。请将用户的原始内容、结构化笔记和对话历史整合成一份完整、有条理的最终笔记。

要求：
1. 保持原有结构化笔记的核心内容
2. 将对话中的重要见解、问题和答案整合进去
3. 确保内容逻辑清晰、层次分明
4. 使用Markdown格式
5. 突出重点和关键信息
6. 如果对话中有深入的分析或新的观点，要重点体现`

    const userPrompt = `请整合以下内容：

**原始内容：**
${originalContent.substring(0, 1000)}${originalContent.length > 1000 ? '...' : ''}

**结构化笔记：**
${structuredNotes}

**对话历史：**
${chatMessages && chatMessages.length > 0 ? 
  chatMessages.map((msg: any) => `${msg.role === 'user' ? '用户' : 'AI'}：${msg.content}`).join('\n\n') : 
  '暂无对话内容'}

请生成整合后的最终笔记：`

    const completion = await openai.chat.completions.create({
      model: process.env.OPENAI_MODEL || 'gpt-4o-mini',
      messages: [
        {
          role: 'system',
          content: systemPrompt
        },
        {
          role: 'user',
          content: userPrompt
        }
      ],
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.5'),
      max_tokens: parseInt(process.env.OPENAI_MAX_TOKENS || '2000')
    })

    const integratedNotes = completion.choices[0]?.message?.content || structuredNotes

    return NextResponse.json({
      integratedNotes
    })

  } catch (error) {
    console.error('Error integrating notes:', error)
    return NextResponse.json(
      { error: '整合笔记失败' },
      { status: 500 }
    )
  }
}
