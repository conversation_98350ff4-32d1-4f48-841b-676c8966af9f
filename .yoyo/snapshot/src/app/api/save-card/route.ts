import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { title, content, sourceUrl } = await request.json()

    if (!title || !content) {
      return NextResponse.json(
        { error: 'Title and content are required' },
        { status: 400 }
      )
    }

    const card = await prisma.card.create({
      data: {
        title,
        content,
        sourceUrl
      }
    })

    return NextResponse.json(card)

  } catch (error) {
    console.error('Error saving card:', error)
    return NextResponse.json(
      { error: 'Failed to save card' },
      { status: 500 }
    )
  }
} 