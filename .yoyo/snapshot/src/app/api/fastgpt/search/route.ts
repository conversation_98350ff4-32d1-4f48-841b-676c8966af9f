import { NextRequest, NextResponse } from 'next/server'
import { 
  searchKnowledgeBase, 
  getDefaultSearchOptions, 
  getReflectionSearchOptions,
  formatSearchResults,
  type SearchOptions 
} from '@/lib/fastgpt'

export async function POST(request: NextRequest) {
  try {
    const { 
      query, 
      mode = 'default',  // 'default', 'reflection', 'custom'
      options = {} 
    } = await request.json()
    
    if (!query) {
      return NextResponse.json({ error: 'query required' }, { status: 400 })
    }

    let searchOptions: SearchOptions
    
    // 根据模式选择搜索选项
    switch (mode) {
      case 'reflection':
        searchOptions = getReflectionSearchOptions()
        break
      case 'custom':
        searchOptions = { ...getDefaultSearchOptions(), ...options }
        break
      case 'default':
      default:
        searchOptions = getDefaultSearchOptions()
        break
    }

    // 如果提供了自定义选项，覆盖默认选项
    if (options && Object.keys(options).length > 0) {
      searchOptions = { ...searchOptions, ...options }
    }

    const results = await searchKnowledgeBase(query, searchOptions)
    
    // 格式化结果（可选）
    const formattedContext = formatSearchResults(results)
    
    return NextResponse.json({ 
      results,
      formattedContext,
      searchOptions,
      count: results.length,
      mode
    })
  } catch (error) {
    console.error('[FastGPT search] Error:', error)
    return NextResponse.json({ error: 'search failed' }, { status: 500 })
  }
} 