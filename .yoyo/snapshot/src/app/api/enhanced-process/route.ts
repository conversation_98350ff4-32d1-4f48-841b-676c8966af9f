import { NextRequest, NextResponse } from 'next/server'
import { scrapeWebPage } from '@/lib/scraper'
import { detectContentType, ContentType } from '@/lib/content-detector'
import { contentSegmenter } from '@/lib/contentSegmenter'
import { enhancedAIGenerator } from '@/lib/enhancedAIGenerator'
import { locationMarkupParser } from '@/lib/locationMarkupParser'

/**
 * 增强的内容处理API
 * 支持生成带定位标记的结构化笔记
 */

// 智能标题生成函数
async function generateSmartTitle(input: string, contentType: ContentType, webTitle?: string): Promise<string> {
  if (contentType === ContentType.URL) {
    if (webTitle) {
      const cleanTitle = webTitle
        .replace(/\s*[-–—|]\s*.*$/, '')
        .replace(/\s*_.*$/, '')
        .replace(/\s*\(.*\)$/, '')
        .replace(/\s*【.*】$/, '')
        .trim()

      return cleanTitle.length > 10 ? cleanTitle.substring(0, 10) : cleanTitle || '网页内容'
    } else {
      try {
        const url = new URL(input)
        const domain = url.hostname.replace('www.', '')
        return domain.length > 10 ? domain.substring(0, 10) : domain
      } catch (error) {
        return '网页链接'
      }
    }
  } else {
    // 对于文本内容，使用简单的标题生成
    const words = input.trim().split(/\s+/).slice(0, 5).join(' ')
    return words.length > 10 ? words.substring(0, 10) : words || '新内容'
  }
}

export async function POST(request: NextRequest) {
  try {
    const { input, type } = await request.json()

    if (!input) {
      return NextResponse.json(
        { error: '缺少输入内容' },
        { status: 400 }
      )
    }

    // 自动检测内容类型
    const detectionResult = type ?
      { type: type === 'url' ? ContentType.URL : ContentType.TEXT } :
      detectContentType(input)

    let title: string
    let content: string
    let textContent: string
    let webTitle: string | undefined

    // 处理不同类型的输入
    if (detectionResult.type === ContentType.URL || type === 'url') {
      try {
        const urlData = await scrapeWebPage(input)
        webTitle = urlData.title
        content = urlData.content
        textContent = urlData.textContent
        title = await generateSmartTitle(input, ContentType.URL, webTitle)
      } catch (error) {
        console.error('网页抓取失败:', error)
        return NextResponse.json(
          {
            error: '无法获取网页内容。可能原因：1) 网站拒绝访问 2) 网络连接问题 3) 网页需要JavaScript渲染',
            success: false
          },
          { status: 500 }
        )
      }
    } else {
      content = input
      textContent = input
      title = await generateSmartTitle(input, ContentType.TEXT)
    }

    // 生成文本片段
    console.log('开始生成文本片段...')
    const textSegments = contentSegmenter.segmentText(textContent)
    console.log(`生成了 ${textSegments.length} 个文本片段`)

    // 生成带定位标记的结构化笔记
    console.log('开始生成带定位标记的结构化笔记...')
    const aiGeneratedContent = await enhancedAIGenerator.generateStructuredNotesWithLocation(
      textContent,
      title,
      textSegments
    )

    // 解析定位标记
    console.log('解析定位标记...')
    const parsedContent = locationMarkupParser.parseContent(aiGeneratedContent, textSegments)
    
    console.log(`解析结果: 
      - 是否包含定位标记: ${parsedContent.hasLocationMarks}
      - 映射关系数量: ${parsedContent.noteReferences.length}
      - 干净内容长度: ${parsedContent.cleanContent.length}`)

    return NextResponse.json({
      title,
      content,
      textContent,
      textSegments,
      aiNote: parsedContent.cleanContent,
      aiNoteWithMarkup: aiGeneratedContent, // 保留原始标记用于调试
      noteReferences: parsedContent.noteReferences,
      hasLocationMarks: parsedContent.hasLocationMarks,
      contentType: detectionResult.type,
      success: true,
      statistics: {
        segmentCount: textSegments.length,
        mappingCount: parsedContent.noteReferences.length,
        averageConfidence: parsedContent.noteReferences.reduce((sum, ref) => sum + ref.confidence, 0) / parsedContent.noteReferences.length || 0
      }
    })

  } catch (error) {
    console.error('增强处理请求失败:', error)
    return NextResponse.json(
      {
        error: error instanceof Error ? error.message : '处理失败',
        success: false
      },
      { status: 500 }
    )
  }
}

// 流式处理端点
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const input = searchParams.get('input')
  const type = searchParams.get('type')

  if (!input) {
    return NextResponse.json(
      { error: '缺少输入内容' },
      { status: 400 }
    )
  }

  try {
    // 处理输入内容（简化版）
    let textContent = input
    let title = '流式内容'
    
    if (type === 'url') {
      try {
        const urlData = await scrapeWebPage(input)
        textContent = urlData.textContent
        title = urlData.title?.substring(0, 10) || '网页内容'
      } catch (error) {
        console.error('网页抓取失败:', error)
        return NextResponse.json({ error: '网页抓取失败' }, { status: 500 })
      }
    }

    // 生成文本片段
    const textSegments = contentSegmenter.segmentText(textContent)

    // 创建流式响应
    const encoder = new TextEncoder()
    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 发送初始数据
          const initialData = {
            type: 'init',
            title,
            textSegments,
            segmentCount: textSegments.length
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(initialData)}\n\n`))

          // 获取AI流式生成
          const aiStream = await enhancedAIGenerator.streamStructuredNotesWithLocation(
            textContent,
            title,
            textSegments
          )

          let accumulatedContent = ''

          for await (const chunk of aiStream) {
            const content = chunk.choices[0]?.delta?.content || ''
            accumulatedContent += content

            // 尝试解析当前累积的内容
            const parsedContent = locationMarkupParser.parseStreamingContent(
              accumulatedContent,
              textSegments
            )

            const streamData = {
              type: 'content',
              content: parsedContent.cleanContent,
              hasLocationMarks: parsedContent.hasLocationMarks,
              noteReferences: parsedContent.noteReferences,
              rawContent: accumulatedContent
            }

            controller.enqueue(encoder.encode(`data: ${JSON.stringify(streamData)}\n\n`))
          }

          // 发送完成信号
          const finalData = {
            type: 'complete',
            finalContent: locationMarkupParser.parseContent(accumulatedContent, textSegments)
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalData)}\n\n`))

        } catch (error) {
          console.error('流式生成失败:', error)
          const errorData = {
            type: 'error',
            error: error instanceof Error ? error.message : '生成失败'
          }
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorData)}\n\n`))
        } finally {
          controller.close()
        }
      }
    })

    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
      },
    })

  } catch (error) {
    console.error('流式处理失败:', error)
    return NextResponse.json(
      { error: '流式处理失败' },
      { status: 500 }
    )
  }
}
