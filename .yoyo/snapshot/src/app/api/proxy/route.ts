import { NextRequest, NextResponse } from 'next/server'
import { chromium } from 'playwright'

interface ProxyOptions {
  url: string
  mode?: 'iframe' | 'proxy' | 'render' | 'auto'
  timeout?: number
}

// 检查URL是否支持iframe嵌入
function isIframeCompatible(url: string): boolean {
  try {
    const urlObj = new URL(url)
    // 已知不支持iframe的域名
    const blockedDomains = [
      'youtube.com', 'youtu.be',
      'facebook.com', 'fb.com',
      'twitter.com', 'x.com',
      'instagram.com',
      'linkedin.com',
      'tiktok.com',
      'github.com',
      'stackoverflow.com'
    ]
    
    return !blockedDomains.some(domain => 
      urlObj.hostname.includes(domain) || 
      urlObj.hostname.endsWith(`.${domain}`)
    )
  } catch {
    return false
  }
}

// 使用Playwright渲染页面
async function renderPage(url: string, timeout: number = 30000): Promise<string> {
  let browser = null
  try {
    browser = await chromium.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    })
    
    const page = await browser.newPage()
    
    // 设置用户代理
    await page.setExtraHTTPHeaders({
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
    })
    
    // 设置视口
    await page.setViewportSize({ width: 1280, height: 720 })
    
    // 导航到页面
    await page.goto(url, { 
      waitUntil: 'networkidle',
      timeout 
    })
    
    // 等待页面完全加载
    await page.waitForTimeout(2000)
    
    // 获取页面内容
    const content = await page.content()
    
    return content
  } catch (error) {
    console.error('Playwright rendering failed:', error)
    throw new Error(`页面渲染失败: ${error instanceof Error ? error.message : '未知错误'}`)
  } finally {
    if (browser) {
      await browser.close()
    }
  }
}

// 简单的HTTP代理
async function proxyRequest(url: string): Promise<string> {
  try {
    const response = await fetch(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'DNT': '1',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
      },
      redirect: 'follow'
    })
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    
    const content = await response.text()
    return content
  } catch (error) {
    console.error('Proxy request failed:', error)
    throw new Error(`代理请求失败: ${error instanceof Error ? error.message : '未知错误'}`)
  }
}

// 处理HTML内容，修复相对路径
function processHtmlContent(html: string, baseUrl: string): string {
  try {
    const urlObj = new URL(baseUrl)
    const baseHref = `${urlObj.protocol}//${urlObj.host}`
    
    // 添加base标签
    let processedHtml = html.replace(
      /<head>/i,
      `<head><base href="${baseHref}">`
    )
    
    // 添加安全策略
    processedHtml = processedHtml.replace(
      /<head>/i,
      `<head>
        <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' *; img-src 'self' data: *; media-src 'self' *;">
        <style>
          body { margin: 0; padding: 20px; font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; }
          .proxy-notice { 
            position: fixed; 
            top: 0; 
            left: 0; 
            right: 0; 
            background: #f0f9ff; 
            border-bottom: 1px solid #0ea5e9; 
            padding: 8px 16px; 
            font-size: 12px; 
            color: #0369a1; 
            z-index: 9999; 
          }
          body { padding-top: 40px !important; }
        </style>`
    )
    
    // 添加代理提示
    processedHtml = processedHtml.replace(
      /<body[^>]*>/i,
      `$&<div class="proxy-notice">📡 通过代理服务器访问 - 某些功能可能受限</div>`
    )
    
    return processedHtml
  } catch (error) {
    console.error('HTML processing failed:', error)
    return html
  }
}

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const url = searchParams.get('url')
  const mode = searchParams.get('mode') as 'iframe' | 'proxy' | 'render' | 'auto' || 'auto'
  const timeout = parseInt(searchParams.get('timeout') || '30000')
  
  if (!url) {
    return NextResponse.json(
      { error: '缺少URL参数' },
      { status: 400 }
    )
  }
  
  try {
    // 验证URL
    new URL(url)
    
    // 自动选择模式
    let selectedMode = mode
    if (mode === 'auto') {
      selectedMode = isIframeCompatible(url) ? 'iframe' : 'render'
    }
    
    let content: string
    let contentType = 'text/html'
    
    switch (selectedMode) {
      case 'iframe':
        // 返回iframe兼容性信息
        return NextResponse.json({
          compatible: true,
          mode: 'iframe',
          url: url,
          message: '该网站支持iframe嵌入'
        })
        
      case 'proxy':
        content = await proxyRequest(url)
        content = processHtmlContent(content, url)
        break
        
      case 'render':
        content = await renderPage(url, timeout)
        content = processHtmlContent(content, url)
        break
        
      default:
        throw new Error('不支持的代理模式')
    }
    
    return new NextResponse(content, {
      headers: {
        'Content-Type': contentType,
        'X-Proxy-Mode': selectedMode,
        'X-Original-URL': url,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    })
    
  } catch (error) {
    console.error('Proxy API error:', error)
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : '代理请求失败',
        url: url,
        mode: mode
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { url, mode = 'auto', timeout = 30000 }: ProxyOptions = await request.json()
    
    if (!url) {
      return NextResponse.json(
        { error: '缺少URL参数' },
        { status: 400 }
      )
    }
    
    // 检查兼容性
    const compatible = isIframeCompatible(url)
    
    return NextResponse.json({
      url,
      compatible,
      recommendedMode: compatible ? 'iframe' : 'render',
      supportedModes: ['iframe', 'proxy', 'render'],
      message: compatible 
        ? '该网站支持iframe嵌入，建议直接使用iframe模式' 
        : '该网站不支持iframe嵌入，建议使用渲染模式'
    })
    
  } catch (error) {
    console.error('Proxy check error:', error)
    return NextResponse.json(
      { error: '检查失败' },
      { status: 500 }
    )
  }
}
