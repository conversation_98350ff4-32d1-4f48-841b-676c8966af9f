import { NextRequest, NextResponse } from 'next/server'
import { scrapeWebPage } from '@/lib/scraper'
import { generateStructuredNoteWithMapping } from '@/lib/ai-mapping'
import { prisma } from '@/lib/prisma'
import { TextSegment, NoteReference } from '@/lib/store'
import crypto from 'crypto'

// 从文本内容中提取段落片段
function extractTextSegments(content: string): TextSegment[] {
  const segments: TextSegment[] = []
  const paragraphs = content.split('\n\n').filter(p => p.trim())
  let currentOffset = 0

  paragraphs.forEach((paragraph, index) => {
    const trimmedContent = paragraph.trim()
    if (!trimmedContent) return

    const segmentId = `seg-${index}-${Date.now()}`
    const hash = crypto.createHash('md5').update(trimmedContent).digest('hex').substring(0, 8)

    segments.push({
      id: segmentId,
      content: trimmedContent,
      startOffset: currentOffset,
      endOffset: currentOffset + trimmedContent.length,
      type: 'paragraph',
      hash,
      metadata: {
        level: 0,
        listIndex: -1
      }
    })

    currentOffset += paragraph.length + 2 // +2 for \n\n
  })

  return segments
}

export async function POST(request: NextRequest) {
  try {
    const { url, text } = await request.json()

    if (!url && !text) {
      return NextResponse.json({ error: 'URL or text is required' }, { status: 400 })
    }

    let originalContent: string
    let title: string
    let textSegments: TextSegment[]

    if (url) {
      // 检查是否已经处理过这个URL
      const existingUrl = await prisma.processedUrl.findUnique({
        where: { url }
      })

      if (existingUrl) {
        originalContent = existingUrl.content
        title = existingUrl.title
        textSegments = extractTextSegments(originalContent)
      } else {
        // 抓取网页内容
        console.log(`Processing URL with mapping: ${url}`)
        const scrapedContent = await scrapeWebPage(url)
        originalContent = scrapedContent.textContent
        title = scrapedContent.title
        textSegments = extractTextSegments(originalContent)

        // 保存处理结果到数据库
        await prisma.processedUrl.create({
          data: {
            url,
            title,
            content: originalContent,
            originalHtml: scrapedContent.content
          }
        })
      }
    } else {
      // 处理纯文本
      originalContent = text
      title = '文本笔记'
      textSegments = extractTextSegments(originalContent)
    }

    // 生成带映射的结构化笔记
    const { aiNote, noteReferences } = await generateStructuredNoteWithMapping(
      originalContent,
      textSegments,
      title
    )

    return NextResponse.json({
      originalContent,
      aiNote,
      textSegments,
      noteReferences,
      title,
      hasLocationMarks: true
    })

  } catch (error) {
    console.error('Error processing with mapping:', error)
    return NextResponse.json(
      { error: 'Failed to process content with mapping' },
      { status: 500 }
    )
  }
}