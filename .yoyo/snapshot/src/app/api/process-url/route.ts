import { NextRequest, NextResponse } from 'next/server'
import { scrapeWebPage } from '@/lib/scraper'
import { generateKnowledgeCards } from '@/lib/ai'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest) {
  try {
    const { url } = await request.json()

    if (!url) {
      return NextResponse.json({ error: 'URL is required' }, { status: 400 })
    }

    // 检查是否已经处理过这个URL
    const existingUrl = await prisma.processedUrl.findUnique({
      where: { url }
    })

    if (existingUrl) {
      // 如果已经处理过，直接返回缓存的结果
      const knowledgeCards = await generateKnowledgeCards(existingUrl.content, url)
      return NextResponse.json({
        originalContent: existingUrl.content,
        knowledgeCards,
        title: existingUrl.title,
        cached: true
      })
    }

    // 抓取网页内容
    console.log(`Processing URL: ${url}`)
    const scrapedContent = await scrapeWebPage(url)

    // 生成知识卡片
    const knowledgeCards = await generateKnowledgeCards(scrapedContent.textContent, url)

    // 保存处理结果到数据库
    await prisma.processedUrl.create({
      data: {
        url,
        title: scrapedContent.title,
        content: scrapedContent.textContent,
        originalHtml: scrapedContent.content
      }
    })

    return NextResponse.json({
      originalContent: scrapedContent.content,
      knowledgeCards,
      title: scrapedContent.title,
      cached: false
    })

  } catch (error) {
    console.error('Error processing URL:', error)
    return NextResponse.json(
      { error: 'Failed to process URL' },
      { status: 500 }
    )
  }
} 