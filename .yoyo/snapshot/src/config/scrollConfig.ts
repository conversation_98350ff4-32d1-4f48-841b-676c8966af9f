/**
 * 滚动体验配置文件
 * 统一管理所有滚动相关的阈值和设置
 */

export interface ScrollConfig {
  // 自动折叠/展开配置
  autoCollapse: {
    /** 笔记区域高度达到视口高度的多少倍时才考虑自动折叠 */
    minNoteLongRatio: number
    /** 滚动超过笔记区域高度的多少比例时触发自动折叠 */
    autoCollapsePercent: number
    /** 滚动回到顶部多少像素内时自动展开 */
    autoExpandTopPx: number
    /** 折叠后的固定高度（像素） */
    collapsedHeightPx: number
  }

  // 手动操作优先级配置
  manualOverride: {
    /** 手动操作后多长时间内禁用自动逻辑（毫秒） */
    duration: number
  }

  // 滚动跟随配置
  scrollFollow: {
    /** 距离底部多少像素时认为在底部 */
    threshold: number
    /** 滚动事件节流间隔（毫秒） */
    throttleMs: number
    /** 防抖延迟（毫秒） */
    debounceMs: number
    /** 最小滚动距离才认为是用户滚动 */
    minScrollDelta: number
  }

  // 性能优化配置
  performance: {
    /** 滚动监听的 requestAnimationFrame 节流 */
    useRaf: boolean
    /** MutationObserver 的防抖延迟 */
    mutationDebounce: number
    /** 内容变化时的滚动延迟 */
    contentChangeScrollDelay: number
  }

  // 移动端适配
  mobile: {
    /** 移动端断点宽度 */
    breakpoint: number
    /** 移动端是否启用折叠功能 */
    enableCollapse: boolean
  }

  // 无障碍配置
  accessibility: {
    /** 键盘导航支持 */
    keyboardNavigation: boolean
    /** ARIA 标签 */
    ariaLabels: {
      expandButton: string
      collapseButton: string
      scrollToBottom: string
      notesSection: string
      originalSection: string
    }
  }

  // 动画配置
  animation: {
    /** 折叠/展开动画持续时间（毫秒） */
    collapseDuration: number
    /** 滚动动画是否使用平滑滚动 */
    smoothScroll: boolean
    /** 移动端标签页切换动画持续时间（毫秒） */
    tabSwitchDuration: number
  }
}

/** 默认滚动配置 */
export const DEFAULT_SCROLL_CONFIG: ScrollConfig = {
  autoCollapse: {
    minNoteLongRatio: 1.5,
    autoCollapsePercent: 0.8,
    autoExpandTopPx: 100,
    collapsedHeightPx: 128
  },

  manualOverride: {
    duration: 2000
  },

  scrollFollow: {
    threshold: 50,
    throttleMs: 16,
    debounceMs: 150,
    minScrollDelta: 10
  },

  performance: {
    useRaf: true,
    mutationDebounce: 50,
    contentChangeScrollDelay: 50
  },

  mobile: {
    breakpoint: 768,
    enableCollapse: false
  },

  accessibility: {
    keyboardNavigation: true,
    ariaLabels: {
      expandButton: '展开结构化笔记',
      collapseButton: '折叠结构化笔记',
      scrollToBottom: '滚动到底部',
      notesSection: '结构化笔记区域',
      originalSection: '原文内容区域'
    }
  },

  animation: {
    collapseDuration: 300,
    smoothScroll: true,
    tabSwitchDuration: 300
  }
}

/** 开发环境配置（更敏感的设置用于调试） */
export const DEV_SCROLL_CONFIG: Partial<ScrollConfig> = {
  scrollFollow: {
    threshold: 30,
    throttleMs: 8,
    debounceMs: 100,
    minScrollDelta: 5
  },
  performance: {
    useRaf: true,
    mutationDebounce: 16,
    contentChangeScrollDelay: 16
  }
}

/** 性能优先配置（适用于低端设备） */
export const PERFORMANCE_SCROLL_CONFIG: Partial<ScrollConfig> = {
  scrollFollow: {
    threshold: 100,
    throttleMs: 32,
    debounceMs: 300,
    minScrollDelta: 20
  },
  performance: {
    useRaf: true,
    mutationDebounce: 200,
    contentChangeScrollDelay: 100
  },
  animation: {
    collapseDuration: 200,
    smoothScroll: false,
    tabSwitchDuration: 200
  }
}

/**
 * 获取当前环境的滚动配置
 */
export function getScrollConfig(): ScrollConfig {
  const isDev = process.env.NODE_ENV === 'development'
  const isPerformanceMode = typeof window !== 'undefined' && 
    (window.navigator.hardwareConcurrency < 4 || 
     window.navigator.connection?.effectiveType === 'slow-2g' ||
     window.navigator.connection?.effectiveType === '2g' ||
     window.navigator.connection?.effectiveType === '3g')

  let config = { ...DEFAULT_SCROLL_CONFIG }

  if (isDev) {
    config = { ...config, ...DEV_SCROLL_CONFIG }
  }

  if (isPerformanceMode) {
    config = { ...config, ...PERFORMANCE_SCROLL_CONFIG }
  }

  return config
}

/**
 * 埋点事件名称常量
 */
export const SCROLL_EVENTS = {
  NOTE_AUTO_COLLAPSE: 'note_auto_collapse',
  NOTE_AUTO_EXPAND: 'note_auto_expand',
  NOTE_MANUAL_COLLAPSE: 'note_manual_collapse',
  NOTE_MANUAL_EXPAND: 'note_manual_expand',
  SCROLL_TO_BOTTOM_CLICK: 'scroll_to_bottom_click',
  SCROLL_FOLLOW_PAUSE: 'scroll_follow_pause',
  SCROLL_FOLLOW_RESUME: 'scroll_follow_resume',
  MOBILE_TAB_SWITCH: 'mobile_tab_switch'
} as const

/**
 * 发送滚动相关埋点
 */
export function trackScrollEvent(
  eventName: keyof typeof SCROLL_EVENTS,
  additionalData?: Record<string, any>
) {
  if (typeof window !== 'undefined' && (window as any).gtag) {
    (window as any).gtag('event', SCROLL_EVENTS[eventName], {
      event_category: 'scroll_interaction',
      ...additionalData
    })
  }
}