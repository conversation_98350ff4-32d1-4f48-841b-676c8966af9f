# 滚动体验优化方案

## 概述

本方案实现了一套完整的流式滚动交互系统，针对结构化笔记和原文的阅读体验进行了深度优化。

## 核心特性

### 🎯 智能折叠/展开策略

- **自动折叠条件**：
  - 笔记高度 > 1.5 × 视口高度
  - 滚动超过笔记区域的 80%
- **自动展开条件**：
  - 滚动回到顶部 100px 内
- **手动操作优先级**：手动操作后 2 秒内禁用自动逻辑

### 📱 移动端适配

- **标签页模式**：≤ 768px 时切换为标签页视图
- **平滑切换动画**：300ms 动画过渡
- **触摸友好**：优化的点击区域和手势支持

### ⚡ 性能优化

- **滚动节流**：16ms 间隔，使用 RAF 优化
- **防抖处理**：150ms 防抖避免频繁触发
- **内存管理**：自动清理定时器和监听器
- **性能监控**：实时监控滚动性能，动态调整参数

### 🔄 流式生成跟随

- **智能跟随**：仅在用户位于底部时自动滚动
- **手动暂停**：用户滚动时自动暂停跟随
- **一键恢复**："滚到底部"按钮快速恢复跟随

## 文件结构

```
src/
├── config/
│   └── scrollConfig.ts          # 全局滚动配置
├── hooks/
│   ├── useSmartScrollFollow.ts  # 智能滚动跟随
│   └── useScrollPerformance.ts  # 性能监控
├── components/ui/
│   ├── CardFlowLayout.tsx       # 主布局组件
│   └── OptimizedStructuredNotes.tsx # 笔记组件
└── docs/
    └── SCROLL_OPTIMIZATION.md   # 本文档
```

## 配置说明

### 基础配置 (`scrollConfig.ts`)

```typescript
export const DEFAULT_SCROLL_CONFIG = {
  autoCollapse: {
    minNoteLongRatio: 1.5,      // 最小长度比例
    autoCollapsePercent: 0.8,   // 折叠触发百分比
    autoExpandTopPx: 100,       // 展开触发距离
    collapsedHeightPx: 128      // 折叠后高度
  },
  scrollFollow: {
    threshold: 50,              // 底部检测阈值
    throttleMs: 16,             // 节流间隔
    debounceMs: 150,            // 防抖延迟
    minScrollDelta: 10          // 最小滚动距离
  },
  // ... 更多配置
}
```

### 性能配置

```typescript
// 开发环境：更敏感的设置
export const DEV_SCROLL_CONFIG = {
  scrollFollow: {
    threshold: 30,
    debounceMs: 100
  }
}

// 性能模式：适用于低端设备
export const PERFORMANCE_SCROLL_CONFIG = {
  scrollFollow: {
    threshold: 100,
    debounceMs: 300
  },
  animation: {
    smoothScroll: false
  }
}
```

## 使用方法

### 1. 基础使用

```typescript
import { useSmartScrollFollow } from '@/hooks/useSmartScrollFollow'

function MyComponent() {
  const {
    containerRef,
    showScrollButton,
    forceScrollToBottom,
    isAtBottom
  } = useSmartScrollFollow({
    enabled: isStreaming,
    threshold: 50
  })

  return (
    <div ref={containerRef} className="overflow-y-auto">
      {/* 内容 */}
      {showScrollButton && (
        <button onClick={forceScrollToBottom}>
          滚动到底部
        </button>
      )}
    </div>
  )
}
```

### 2. 集成性能监控

```typescript
import { useScrollPerformance } from '@/hooks/useScrollPerformance'

function PerformanceAwareComponent() {
  const {
    measureScrollEvent,
    isPerformanceGood,
    getAdaptiveScrollConfig
  } = useScrollPerformance()

  const handleScroll = () => {
    measureScrollEvent(() => {
      // 滚动处理逻辑
    })
  }

  const config = getAdaptiveScrollConfig()
  // 根据性能动态调整配置
}
```

### 3. 埋点集成

```typescript
import { trackScrollEvent } from '@/config/scrollConfig'

// 手动折叠
trackScrollEvent('NOTE_MANUAL_COLLAPSE')

// 滚动到底部点击
trackScrollEvent('SCROLL_TO_BOTTOM_CLICK', {
  streamingContent: true
})
```

## 交互行为

### 桌面端

1. **初始状态**：笔记区域展开，占满首屏
2. **长内容自动折叠**：滚动超过 80% 时自动折叠到 128px
3. **折叠状态**：显示标题和"展开查看"按钮
4. **自动展开**：滚动回顶部 100px 内时自动展开
5. **手动优先**：手动操作后 2 秒内禁用自动逻辑

### 移动端

1. **标签页模式**：上方固定标签栏（笔记/原文）
2. **平滑切换**：300ms 动画过渡
3. **状态指示**：活跃标签页高亮显示
4. **触摸优化**：增大点击区域，优化手势

### 流式生成

1. **自动跟随**：默认跟随到底部
2. **用户滚动暂停**：检测到手动滚动时暂停
3. **一键恢复**：显示"滚到底部"按钮
4. **状态反馈**：实时显示跟随状态

## 性能指标

### 目标性能

- **滚动事件处理**：平均 < 16ms，最大 < 50ms
- **帧率**：保持 > 50fps（< 20ms 帧间隔）
- **内存使用**：< 80% 堆内存
- **响应时间**：用户操作响应 < 100ms

### 监控指标

- 滚动事件数量和处理时间
- 帧丢失统计
- 内存使用率
- 自动优化触发次数

## 无障碍支持

### 键盘导航

- `Tab` / `Shift+Tab`：在元素间导航
- `Space` / `Enter`：触发折叠/展开
- `←` / `→`：在导航按钮间切换

### ARIA 标签

- `aria-expanded`：折叠/展开状态
- `aria-current`：当前激活区域
- `aria-label`：按钮功能描述
- `role="button"`：可点击元素

### 屏幕阅读器

- 清晰的状态描述
- 操作结果反馈
- 内容结构导航

## 调试工具

### 开发环境

```javascript
// 在浏览器控制台查看性能报告
window.scrollDebug = true

// 查看配置
console.log(getScrollConfig())

// 强制触发性能报告
window.reportScrollPerformance?.()
```

### 埋点数据

- Google Analytics 事件追踪
- 自定义性能指标
- 用户行为分析

## 最佳实践

1. **配置调优**：根据用户反馈调整阈值
2. **性能监控**：定期检查性能报告
3. **用户测试**：不同设备和网络环境测试
4. **渐进增强**：确保基础功能在所有环境下可用
5. **错误处理**：优雅降级到基础滚动行为

## 常见问题

### Q: 自动折叠太敏感怎么办？
A: 调整 `autoCollapsePercent` 值，或增加 `minNoteLongRatio`

### Q: 移动端体验不佳？
A: 检查 `mobile.breakpoint` 设置，确保标签页模式正确触发

### Q: 性能问题？
A: 启用性能监控，查看具体指标，考虑使用 `PERFORMANCE_SCROLL_CONFIG`

### Q: 流式跟随不工作？
A: 检查 `enabled` 参数和 `shouldAutoFollow` 状态