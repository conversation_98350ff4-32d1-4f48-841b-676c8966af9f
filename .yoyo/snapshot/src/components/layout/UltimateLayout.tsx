'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Plus, Edit3, ChevronDown } from 'lucide-react'
import WorkArea from './WorkArea'
import AIAssistant from './AIAssistant'
import DynamicBackground from '../ui/DynamicBackground'
import { cn } from '@/lib/utils'
import BentoNoteView from '../content/BentoNoteView'
import OriginalContentViewer from '../content/OriginalContentViewer'

const UltimateLayout: React.FC = () => {
  const { 
    tabs, 
    middlePanelWidth,
    setMiddlePanelWidth
  } = useAppStore()
  
  const [isDraggingRight, setIsDraggingRight] = useState(false)
  const [isEditMode, setIsEditMode] = useState(false)
  const [showOriginalDrawer, setShowOriginalDrawer] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 获取当前活跃标签页
  const activeTab = tabs.find(tab => tab.id === useAppStore.getState().activeTabId)
  const isEmptyNewTab = activeTab &&
    activeTab.sourceData === '' &&
    activeTab.originalContent === '' &&
    activeTab.aiNoteMarkdown === ''

  // 处理右侧分隔条拖拽
  const handleRightDividerMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDraggingRight(true)
  }, [])

  // 处理拖拽过程
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!containerRef.current) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const mouseX = ((e.clientX - containerRect.left) / containerRect.width) * 100

    if (isDraggingRight) {
      // 限制中栏宽度范围：60% - 80%
      const newMiddleWidth = Math.max(60, Math.min(80, mouseX))
      setMiddlePanelWidth(newMiddleWidth)
    }
  }, [isDraggingRight, setMiddlePanelWidth])

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDraggingRight(false)
  }, [])

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDraggingRight) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDraggingRight, handleMouseMove, handleMouseUp])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // E 键切换编辑模式
      if (e.key === 'e' || e.key === 'E') {
        if (!e.ctrlKey && !e.metaKey && !e.altKey && activeTab && activeTab.aiNoteMarkdown) {
          e.preventDefault()
          setIsEditMode(prev => !prev)
        }
      }

      // Cmd/Ctrl + J 聚焦AI助手
      if ((e.ctrlKey || e.metaKey) && e.key === 'j') {
        e.preventDefault()
        const chatInput = document.querySelector('#chat-form input') as HTMLInputElement
        if (chatInput) {
          chatInput.focus()
        }
      }

      // J/K 键导航（仅在阅读模式）
      if (!isEditMode && !e.ctrlKey && !e.metaKey && !e.altKey) {
        if (e.key === 'j' || e.key === 'k') {
          e.preventDefault()
          // TODO: 实现段落间导航
        }
      }

      // Enter 键展开/折叠原文
      if (e.key === 'Enter' && !e.ctrlKey && !e.metaKey && !e.altKey && !isEditMode) {
        e.preventDefault()
        // TODO: 展开当前焦点的原文摘录
      }

      // Esc 键关闭高亮或原文抽屉
      if (e.key === 'Escape') {
        e.preventDefault()
        setShowOriginalDrawer(false)
        // TODO: 清除所有高亮
      }

      // Tab 键在移动端切换标签
      if (e.key === 'Tab' && !e.ctrlKey && !e.metaKey && !e.altKey && window.innerWidth < 1024) {
        e.preventDefault()
        // TODO: 切换原文/笔记标签
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [activeTab, isEditMode])

  return (
    <div className="h-screen adaptive-light flex flex-col relative">
      {/* 动态背景 - 只在没有标签页或空标签页时显示 */}
      {(tabs.length === 0 || isEmptyNewTab) && <DynamicBackground />}

      {/* 动态标签栏 - 只在有标签页时显示 */}
      {tabs.length > 0 && (
        <div className="glass-effect-strong border-b border-border/50 flex items-center px-6 shadow-sm flex-shrink-0">
          <div className="flex items-center">
            <div className="flex items-center overflow-x-auto">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={cn(
                    "flex items-center space-x-3 px-4 py-3 rounded-t-xl cursor-pointer transition-all duration-200 flex-shrink-0",
                    tab.id === useAppStore.getState().activeTabId
                      ? 'glass-effect text-primary border-b-2 border-primary shadow-sm'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  )}
                  onClick={() => useAppStore.getState().setActiveTab(tab.id)}
                >
                  <span className="text-sm font-medium truncate max-w-32">
                    {tab.title}
                  </span>
                  {tab.isLoading && (
                    <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
                  )}
                  {tab.aiAnalyzing && (
                    <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      useAppStore.getState().removeTab(tab.id)
                    }}
                    className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>

            <button
              onClick={() => {
                useAppStore.getState().addTab({
                  title: '新标签页',
                  sourceType: 'text',
                  sourceData: '',
                  originalContent: '',
                  aiNoteMarkdown: '',
                  isLoading: false
                })
              }}
              className="ml-1 p-2 hover:bg-white/60 rounded-lg transition-all duration-200 flex-shrink-0"
              title="新建标签页"
            >
              <Plus size={14} className="text-gray-500" />
            </button>
          </div>

          {/* 编辑模式开关 */}
          {activeTab && activeTab.aiNoteMarkdown && (
            <div className="ml-auto flex items-center space-x-2">
              <button
                onClick={() => setIsEditMode(!isEditMode)}
                className={cn(
                  "flex items-center space-x-2 px-3 py-1.5 rounded-lg transition-all duration-200",
                  isEditMode
                    ? "bg-primary text-white"
                    : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                )}
              >
                <Edit3 size={14} />
                <span className="text-sm">{isEditMode ? '返回阅读' : '编辑'}</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* 主要内容区域 - 三栏布局 */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden min-h-0"
      >
        {/* 左栏 - 原文浏览器（淡化背景） */}
        {tabs.length > 0 && !isEmptyNewTab && (
          <div className="w-[30%] lg:w-[30%] hidden lg:block overflow-hidden bg-gray-50/50 border-r border-gray-200/50">
            <div className="h-full p-4 overflow-y-auto opacity-70 hover:opacity-100 transition-opacity duration-300">
              <div className="mb-2 text-xs text-gray-500 uppercase tracking-wider">原文</div>
              <OriginalContentViewer 
                content={activeTab?.originalContent || ''}
                highlightedSegments={activeTab?.highlightedSegments || []}
              />
            </div>
          </div>
        )}

        {/* 中栏 - 结构化笔记（主要区域） */}
        <div
          className="flex flex-col min-h-0 bg-white"
          style={{
            width: tabs.length > 0 && !isEmptyNewTab ? `${middlePanelWidth}%` : '100%',
            transition: isDraggingRight ? 'none' : 'width 0.15s ease-out'
          }}
        >
          {tabs.length === 0 || isEmptyNewTab ? (
            <WorkArea />
          ) : (
            <div className="flex-1 overflow-y-auto">
              {isEditMode ? (
                // 编辑模式 - Markdown编辑器
                <div className="p-6">
                  <textarea
                    className="w-full h-full min-h-[600px] p-4 border border-gray-200 rounded-lg font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-primary/20"
                    value={activeTab?.aiNoteMarkdown || ''}
                    onChange={(e) => {
                      if (activeTab) {
                        useAppStore.getState().updateTab(activeTab.id, {
                          aiNoteMarkdown: e.target.value
                        })
                      }
                    }}
                    placeholder="在这里编辑你的笔记..."
                  />
                </div>
              ) : (
                // 阅读模式 - Bento卡片视图
                <BentoNoteView 
                  noteContent={activeTab?.aiNoteMarkdown || ''}
                  noteReferences={activeTab?.noteReferences || []}
                  onSegmentClick={(segmentId) => {
                    // TODO: 实现原文定位和高亮
                    console.log('Clicked segment:', segmentId)
                  }}
                />
              )}
            </div>
          )}
        </div>

        {/* 右侧分隔条 */}
        {tabs.length > 0 && !isEmptyNewTab && (
          <>
            <div
              className={cn(
                "group flex-shrink-0 relative cursor-col-resize transition-all duration-150",
                isDraggingRight && "shadow-md"
              )}
              onMouseDown={handleRightDividerMouseDown}
              style={{
                width: isDraggingRight ? '6px' : '4px',
                margin: '0 2px'
              }}
            >
              <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 flex items-center justify-center">
                <div className={cn(
                  "w-0.5 h-12 bg-gradient-to-b from-blue-400 to-purple-500 rounded-full transition-all duration-150",
                  isDraggingRight ? "h-16" : "group-hover:h-16"
                )} />
              </div>
            </div>

            {/* 右栏 - AI助手 */}
            <div
              className="bg-gray-50/50 overflow-hidden flex flex-col"
              style={{
                width: `${100 - middlePanelWidth}%`,
                transition: isDraggingRight ? 'none' : 'width 0.15s ease-out'
              }}
            >
              <AIAssistant />
            </div>
          </>
        )}
      </div>

      {/* 移动端原文抽屉 */}
      <div className={cn(
        "lg:hidden fixed inset-x-0 bottom-0 bg-white border-t border-gray-200 shadow-lg transition-transform duration-300",
        showOriginalDrawer ? "translate-y-0" : "translate-y-full"
      )}
      style={{ maxHeight: '50vh' }}
      >
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h3 className="font-medium">原文摘录</h3>
          <button
            onClick={() => setShowOriginalDrawer(false)}
            className="p-1 hover:bg-gray-100 rounded"
          >
            <ChevronDown size={20} />
          </button>
        </div>
        <div className="p-4 overflow-y-auto" style={{ maxHeight: 'calc(50vh - 60px)' }}>
          <OriginalContentViewer 
            content={activeTab?.originalContent || ''}
            highlightedSegments={activeTab?.highlightedSegments || []}
          />
        </div>
      </div>

      {/* 移动端AI助手浮动按钮 */}
      <button
        className="lg:hidden fixed bottom-4 right-4 w-14 h-14 bg-primary text-white rounded-full shadow-lg flex items-center justify-center"
        onClick={() => {
          // TODO: 打开AI助手对话框
        }}
      >
        💬
      </button>
    </div>
  )
}

export default UltimateLayout