'use client'

import React from 'react'
import { useAppStore } from '@/lib/store'
import { Globe, Brain } from 'lucide-react'

const ModeSwitch: React.FC = () => {
  const { mode, setMode } = useAppStore()

  const modes = [
    {
      id: 'browse' as const,
      name: '浏览模式',
      icon: Globe,
      description: '浏览和分析网页内容'
    },
    {
      id: 'reflection' as const,
      name: '沉思模式',
      icon: Brain,
      description: '查看和管理已保存的笔记'
    }
  ]

  return (
    <div className="fixed left-0 top-0 h-full w-16 bg-white border-r border-gray-200/50 shadow-lg z-50 flex flex-col">
      {/* 模式切换按钮 */}
      <div className="flex-1 flex flex-col justify-center space-y-4 px-2">
        {modes.map((modeItem) => {
          const Icon = modeItem.icon
          const isActive = mode === modeItem.id
          
          return (
            <button
              key={modeItem.id}
              onClick={() => setMode(modeItem.id)}
              className={`group relative w-12 h-12 rounded-xl transition-all duration-300 flex items-center justify-center ${
                isActive
                  ? 'bg-gradient-to-br from-blue-500 to-purple-600 text-white shadow-lg shadow-blue-500/25'
                  : 'bg-gray-50 text-gray-600 hover:bg-gray-100 hover:text-blue-600'
              }`}
              title={modeItem.name}
            >
              <Icon className="w-5 h-5" />
              
              {/* 悬浮提示 */}
              <div className="absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50">
                <div className="font-medium">{modeItem.name}</div>
                <div className="text-xs text-gray-300 mt-1">{modeItem.description}</div>
                {/* 箭头 */}
                <div className="absolute right-full top-1/2 transform -translate-y-1/2 border-4 border-transparent border-r-gray-900"></div>
              </div>
            </button>
          )
        })}
      </div>

      {/* 底部装饰 */}
      <div className="p-2">
        <div className="w-8 h-0.5 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"></div>
      </div>
    </div>
  )
}

export default ModeSwitch
