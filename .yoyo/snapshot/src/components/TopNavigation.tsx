'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { RefreshCw, MessageSquare, Zap } from 'lucide-react'

interface TopNavigationProps {
  onLogoClick: () => void
  onChatClick: () => void
  onSyncClick?: () => void
}

export function TopNavigation({ onLogoClick, onChatClick, onSyncClick }: TopNavigationProps) {
  const [syncing, setSyncing] = useState(false)

  const handleSync = async () => {
    if (onSyncClick) {
      setSyncing(true)
      try {
        await onSyncClick()
        // 显示成功状态
        setTimeout(() => setSyncing(false), 1000)
      } catch (error) {
        setSyncing(false)
      }
    }
  }

  return (
    <header className="w-full border-b border-border bg-white/80 backdrop-blur-sm sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo 和名称 */}
          <div 
            className="flex items-center space-x-3 cursor-pointer group"
            onClick={onLogoClick}
          >
            <div className="flex items-center justify-center w-8 h-8 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors duration-200">
              <Zap className="w-5 h-5 text-primary" />
            </div>
            <div className="hidden sm:block">
              <h1 className="text-lg font-semibold text-foreground">增强阅读器</h1>
              <p className="text-xs text-muted-foreground -mt-1">Augmented Reader</p>
            </div>
          </div>

          {/* 右侧操作按钮 */}
          <div className="flex items-center space-x-3">
            {/* 同步按钮 */}
            {onSyncClick && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSync}
                disabled={syncing}
                className="text-muted-foreground hover:text-foreground transition-colors duration-200"
              >
                <RefreshCw 
                  className={`w-4 h-4 mr-2 ${syncing ? 'animate-spin' : ''}`} 
                />
                <span className="hidden sm:inline">
                  {syncing ? '同步中...' : '同步知识库'}
                </span>
                <span className="sm:hidden">
                  {syncing ? '同步中' : '同步'}
                </span>
              </Button>
            )}

            {/* 对话按钮 */}
            <Button
              variant="default"
              size="sm"
              onClick={onChatClick}
              className="shadow-sm hover:shadow-md transition-all duration-200"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              <span className="hidden sm:inline">知识库对话</span>
              <span className="sm:hidden">对话</span>
            </Button>
          </div>
        </div>
      </div>
    </header>
  )
} 