import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium transition-all duration-200 disabled:pointer-events-none disabled:opacity-50 outline-none focus:outline-none focus:ring-2 focus:ring-offset-2 relative overflow-hidden",
  {
    variants: {
      variant: {
        default:
          "bg-indigo-600 text-white shadow-md hover:bg-indigo-700 hover:shadow-lg focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
        destructive:
          "bg-red-600 text-white shadow-md hover:bg-red-700 hover:shadow-lg focus:ring-red-500 transform hover:scale-105 active:scale-95",
        outline:
          "border border-gray-300 bg-white shadow-sm hover:bg-gray-50 hover:shadow-md focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
        secondary:
          "bg-gray-100 text-gray-900 shadow-sm hover:bg-gray-200 hover:shadow-md focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
        ghost:
          "hover:bg-gray-100 hover:text-gray-900 focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
        link: "text-indigo-600 hover:underline focus:ring-indigo-500",
        gradient:
          "bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 text-white shadow-lg hover:shadow-xl focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
        glass:
          "glass-effect text-gray-900 shadow-md hover:shadow-lg focus:ring-indigo-500 transform hover:scale-105 active:scale-95",
      },
      size: {
        default: "h-10 px-4 py-2 text-sm",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-12 rounded-lg px-6 text-base",
        xl: "h-14 rounded-xl px-8 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8 rounded-md",
        "icon-lg": "h-12 w-12 rounded-lg",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

function Button({
  className,
  variant,
  size,
  asChild = false,
  ...props
}: React.ComponentProps<"button"> &
  VariantProps<typeof buttonVariants> & {
    asChild?: boolean
  }) {
  const Comp = asChild ? Slot : "button"

  return (
    <Comp
      data-slot="button"
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  )
}

export { Button, buttonVariants }
