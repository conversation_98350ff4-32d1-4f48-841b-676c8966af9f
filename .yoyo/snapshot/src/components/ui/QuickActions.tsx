'use client'

import React from 'react'
import { BookO<PERSON>, Clock, Settings } from 'lucide-react'
import { useAppStore } from '@/lib/store'

interface QuickActionsProps {
  className?: string
}

const QuickActions: React.FC<QuickActionsProps> = ({ className = '' }) => {
  const { setMode, loadSavedNotes } = useAppStore()

  const handleNotesClick = () => {
    // 切换到沉思模式查看笔记
    setMode('reflection')
    loadSavedNotes()
  }

  const handleHistoryClick = () => {
    // 这里可以添加历史记录功能
    console.log('历史记录功能待实现')
  }

  const handleSettingsClick = () => {
    // 这里可以添加设置功能
    console.log('设置功能待实现')
  }

  return (
    <div className={`quick-actions ${className}`}>
      <div className="quick-action" onClick={handleNotesClick}>
        <BookOpen />
        <span className="tooltip">我的笔记</span>
      </div>
      
      <div className="quick-action" onClick={handleHistoryClick}>
        <Clock />
        <span className="tooltip">历史记录</span>
      </div>
      
      <div className="quick-action" onClick={handleSettingsClick}>
        <Settings />
        <span className="tooltip">设置</span>
      </div>
    </div>
  )
}

export default QuickActions
