'use client'

import React, { useEffect } from 'react'

interface DataFlowIconProps {
  className?: string
  size?: number
  onClick?: () => void
}

const DataFlowIcon: React.FC<DataFlowIconProps> = ({ 
  className = '', 
  size = 120,
  onClick 
}) => {
  useEffect(() => {
    // 动态连线动画
    const connections = document.querySelectorAll('.connection')
    connections.forEach((line, index) => {
      const element = line as HTMLElement
      element.style.animationDelay = `${index * 0.2}s`
    })
  }, [])

  const handleClick = () => {
    if (onClick) {
      onClick()
    }
    
    // 添加点击动画
    const iconElement = document.querySelector('.app-icon')
    if (iconElement) {
      const element = iconElement as HTMLElement
      element.style.animation = 'spin 0.6s ease'
      setTimeout(() => {
        element.style.animation = ''
      }, 600)
    }
  }

  return (
    <div 
      className={`app-icon ${className}`}
      style={{ width: size, height: size }}
      onClick={handleClick}
    >
      <div className="icon-container">
        <div className="icon-content">
          <div className="data-flow">
            <div className="node"></div>
            <div className="node"></div>
            <div className="node"></div>
            <div className="node"></div>
            <div className="node"></div>
            <svg style={{ position: 'absolute', width: '100%', height: '100%', top: 0, left: 0 }}>
              <line 
                className="connection" 
                x1="30" y1="6" 
                x2="12" y2="24" 
                stroke="url(#gradient)" 
                strokeWidth="2"
              />
              <line 
                className="connection" 
                x1="30" y1="6" 
                x2="48" y2="24" 
                stroke="url(#gradient)" 
                strokeWidth="2"
              />
              <line 
                className="connection" 
                x1="12" y1="24" 
                x2="18" y2="48" 
                stroke="url(#gradient)" 
                strokeWidth="2"
              />
              <line 
                className="connection" 
                x1="48" y1="24" 
                x2="42" y2="48" 
                stroke="url(#gradient)" 
                strokeWidth="2"
              />
              <defs>
                <linearGradient id="gradient">
                  <stop offset="0%" stopColor="transparent"/>
                  <stop offset="50%" stopColor="#6366f1"/>
                  <stop offset="100%" stopColor="transparent"/>
                </linearGradient>
              </defs>
            </svg>
          </div>
        </div>
      </div>
    </div>
  )
}

export default DataFlowIcon
