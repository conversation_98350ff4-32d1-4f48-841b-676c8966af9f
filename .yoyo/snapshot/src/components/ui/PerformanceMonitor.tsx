'use client'

import React, { useState, useEffect } from 'react'
import { Clock, Zap, AlertTriangle } from 'lucide-react'

interface PerformanceMetrics {
  startTime: number
  iframeLoadTime?: number
  aiAnalysisTime?: number
  totalTime?: number
  url: string
}

interface PerformanceMonitorProps {
  url: string
  onMetricsUpdate?: (metrics: PerformanceMetrics) => void
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  url, 
  onMetricsUpdate 
}) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    startTime: Date.now(),
    url
  })
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // 重置计时器
    const newMetrics = {
      startTime: Date.now(),
      url
    }
    setMetrics(newMetrics)
    setIsVisible(true)

    // 5秒后自动隐藏
    const hideTimer = setTimeout(() => {
      setIsVisible(false)
    }, 5000)

    return () => clearTimeout(hideTimer)
  }, [url])

  const updateMetrics = (update: Partial<PerformanceMetrics>) => {
    const updatedMetrics = { ...metrics, ...update }
    setMetrics(updatedMetrics)
    onMetricsUpdate?.(updatedMetrics)
  }

  const getElapsedTime = () => {
    return Date.now() - metrics.startTime
  }

  const getPerformanceStatus = () => {
    const elapsed = getElapsedTime()
    if (elapsed < 500) return { status: 'excellent', color: 'text-green-600', icon: Zap }
    if (elapsed < 1000) return { status: 'good', color: 'text-blue-600', icon: Clock }
    if (elapsed < 2000) return { status: 'fair', color: 'text-yellow-600', icon: Clock }
    return { status: 'slow', color: 'text-red-600', icon: AlertTriangle }
  }

  const performance = getPerformanceStatus()
  const Icon = performance.icon

  if (!isVisible) return null

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-200 rounded-lg shadow-lg p-3 z-50 min-w-64">
      <div className="flex items-center space-x-2 mb-2">
        <Icon size={16} className={performance.color} />
        <span className="text-sm font-medium text-gray-900">加载性能监控</span>
        <button
          onClick={() => setIsVisible(false)}
          className="ml-auto text-gray-400 hover:text-gray-600"
        >
          ×
        </button>
      </div>
      
      <div className="space-y-1 text-xs">
        <div className="flex justify-between">
          <span className="text-gray-600">当前用时:</span>
          <span className={`font-mono ${performance.color}`}>
            {getElapsedTime()}ms
          </span>
        </div>
        
        {metrics.iframeLoadTime && (
          <div className="flex justify-between">
            <span className="text-gray-600">iframe加载:</span>
            <span className="font-mono text-green-600">
              {metrics.iframeLoadTime}ms
            </span>
          </div>
        )}
        
        {metrics.aiAnalysisTime && (
          <div className="flex justify-between">
            <span className="text-gray-600">AI分析:</span>
            <span className="font-mono text-blue-600">
              {metrics.aiAnalysisTime}ms
            </span>
          </div>
        )}
        
        <div className="pt-1 border-t border-gray-100">
          <div className="text-gray-500 truncate" title={url}>
            {new URL(url).hostname}
          </div>
        </div>
        
        <div className="flex items-center space-x-1 pt-1">
          <div className={`w-2 h-2 rounded-full ${
            performance.status === 'excellent' ? 'bg-green-500' :
            performance.status === 'good' ? 'bg-blue-500' :
            performance.status === 'fair' ? 'bg-yellow-500' : 'bg-red-500'
          }`}></div>
          <span className={`text-xs ${performance.color}`}>
            {performance.status === 'excellent' ? '优秀' :
             performance.status === 'good' ? '良好' :
             performance.status === 'fair' ? '一般' : '需优化'}
          </span>
        </div>
      </div>
    </div>
  )
}

// Hook for using performance monitoring
export const usePerformanceMonitor = (url: string) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)
  
  const startMonitoring = () => {
    setMetrics({
      startTime: Date.now(),
      url
    })
  }
  
  const recordIframeLoad = () => {
    if (metrics) {
      const iframeLoadTime = Date.now() - metrics.startTime
      setMetrics(prev => prev ? { ...prev, iframeLoadTime } : null)
      return iframeLoadTime
    }
    return 0
  }
  
  const recordAIAnalysis = () => {
    if (metrics) {
      const aiAnalysisTime = Date.now() - metrics.startTime
      setMetrics(prev => prev ? { ...prev, aiAnalysisTime } : null)
      return aiAnalysisTime
    }
    return 0
  }
  
  return {
    metrics,
    startMonitoring,
    recordIframeLoad,
    recordAIAnalysis
  }
}

export default PerformanceMonitor
