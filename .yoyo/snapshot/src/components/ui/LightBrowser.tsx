'use client'

import React, { useState, useRef, useEffect } from 'react'
import { RefreshCw, ExternalLink, AlertCircle, ArrowLeft, ArrowRight, Home } from 'lucide-react'
import PerformanceMonitor, { usePerformanceMonitor } from './PerformanceMonitor'

interface LightBrowserProps {
  url: string
  title: string
  onLoadComplete?: () => void
  onError?: (error: string) => void
  onNavigateToNewTab?: (url: string) => void // 新增：在新标签页中打开链接的回调
}

type BrowserMode = 'iframe' | 'proxy' | 'render'

interface ProxyResponse {
  compatible: boolean
  mode: BrowserMode
  url: string
  message: string
}

const LightBrowser: React.FC<LightBrowserProps> = ({
  url,
  title,
  onLoadComplete,
  onError,
  onNavigateToNewTab
}) => {
  const [loading, setLoading] = useState(false) // 改为false，立即显示iframe
  const [error, setError] = useState<string | null>(null)
  const [currentUrl, setCurrentUrl] = useState(url)
  const [loadStartTime, setLoadStartTime] = useState<number>(0)
  const [canGoBack, setCanGoBack] = useState(false)
  const [canGoForward, setCanGoForward] = useState(false)
  const [isEditingUrl, setIsEditingUrl] = useState(false)
  const [urlInput, setUrlInput] = useState(url)
  const [history, setHistory] = useState<string[]>([url])
  const [historyIndex, setHistoryIndex] = useState(0)
  const [browserMode, setBrowserMode] = useState<BrowserMode>('iframe')

  const [showPerformanceMonitor, setShowPerformanceMonitor] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)
  const { metrics, startMonitoring, recordIframeLoad } = usePerformanceMonitor(currentUrl)

  // 处理iframe加载开始
  const handleLoadStart = () => {
    setLoadStartTime(Date.now())
    setLoading(true)
    setError(null)
    startMonitoring()
    setShowPerformanceMonitor(true)
  }

  // 处理iframe加载完成
  const handleLoad = () => {
    const loadTime = Date.now() - loadStartTime
    console.log(`页面加载完成，耗时: ${loadTime}ms`)
    setLoading(false)
    setError(null)

    // 记录iframe加载性能
    recordIframeLoad()

    // 尝试注入导航脚本
    setTimeout(() => {
      injectNavigationScript()
    }, 100)

    onLoadComplete?.()
  }

  // 处理iframe加载错误
  const handleError = () => {
    setLoading(false)
    const errorMsg = browserMode === 'iframe'
      ? '此网站不允许在框架中显示，请尝试切换到代理模式或在新窗口打开'
      : '无法加载网页内容'
    setError(errorMsg)
    onError?.(errorMsg)
  }

  // 刷新页面
  const handleRefresh = () => {
    if (iframeRef.current) {
      handleLoadStart()
      iframeRef.current.src = currentUrl
    }
  }

  // 在新窗口打开
  const handleOpenExternal = () => {
    window.open(currentUrl, '_blank')
  }

  // 导航到新URL
  const navigateToUrl = (newUrl: string) => {
    try {
      // 验证URL格式
      const urlObj = new URL(newUrl.startsWith('http') ? newUrl : `https://${newUrl}`)
      const validUrl = urlObj.toString()

      // 更新历史记录
      const newHistory = history.slice(0, historyIndex + 1)
      newHistory.push(validUrl)
      setHistory(newHistory)
      setHistoryIndex(newHistory.length - 1)

      // 更新导航状态
      setCanGoBack(newHistory.length > 1)
      setCanGoForward(false)

      setCurrentUrl(validUrl)
      setUrlInput(validUrl)
      handleLoadStart()
    } catch (error) {
      console.error('Invalid URL:', error)
      setError('无效的网址格式')
    }
  }

  // 后退
  const handleGoBack = () => {
    if (canGoBack && historyIndex > 0) {
      const newIndex = historyIndex - 1
      setHistoryIndex(newIndex)
      setCurrentUrl(history[newIndex])
      setUrlInput(history[newIndex])
      setCanGoBack(newIndex > 0)
      setCanGoForward(true)
      handleLoadStart()
    }
  }

  // 前进
  const handleGoForward = () => {
    if (canGoForward && historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1
      setHistoryIndex(newIndex)
      setCurrentUrl(history[newIndex])
      setUrlInput(history[newIndex])
      setCanGoBack(true)
      setCanGoForward(newIndex < history.length - 1)
      handleLoadStart()
    }
  }

  // 回到首页
  const handleGoHome = () => {
    navigateToUrl(url)
  }

  // 处理地址栏编辑
  const handleUrlSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (urlInput.trim()) {
      navigateToUrl(urlInput.trim())
      setIsEditingUrl(false)
    }
  }

  // 后台检查URL兼容性（不阻塞显示）
  const checkCompatibilityInBackground = async (targetUrl: string) => {
    try {
      const response = await fetch('/api/proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ url: targetUrl, mode: 'auto' }),
        signal: AbortSignal.timeout(5000) // 5秒超时，避免长时间等待
      })

      if (response.ok) {
        const data = await response.json()
        console.log('兼容性检查结果:', data.message)

        // 如果当前是iframe模式但检测到不兼容，显示提示
        if (browserMode === 'iframe' && !data.compatible) {
          console.log('检测到iframe不兼容，建议切换到', data.recommendedMode)
          // 可以在这里显示一个非阻塞的提示，让用户选择是否切换
        }
      }
    } catch (error) {
      // 兼容性检查失败不影响正常使用
      console.log('后台兼容性检查失败:', error)
    }
  }

  // 智能切换浏览器模式
  const switchBrowserMode = (mode: BrowserMode) => {
    setBrowserMode(mode)
    handleLoadStart()
  }

  // 获取当前模式的显示URL
  const getDisplayUrl = () => {
    if (browserMode === 'iframe') {
      return currentUrl
    } else {
      return `/api/proxy?url=${encodeURIComponent(currentUrl)}&mode=${browserMode}`
    }
  }

  // 检查URL是否可以在iframe中显示
  const isIframeCompatible = (url: string) => {
    try {
      const urlObj = new URL(url)
      // 一些网站不允许在iframe中显示
      const blockedDomains = ['youtube.com', 'facebook.com', 'twitter.com', 'instagram.com']
      return !blockedDomains.some(domain => urlObj.hostname.includes(domain))
    } catch {
      return false
    }
  }

  useEffect(() => {
    if (url !== currentUrl) {
      setCurrentUrl(url)
      setUrlInput(url)
      // 立即开始加载，不等待兼容性检查
      handleLoadStart()

      // 后台异步检查兼容性，不阻塞显示
      setTimeout(() => {
        checkCompatibilityInBackground(url)
      }, 100)
    }
  }, [url, currentUrl])

  // 监听iframe内的链接点击事件 - 修改为在新标签页中打开
  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // 只处理来自iframe的消息
      if (event.source === iframeRef.current?.contentWindow) {
        if (event.data.type === 'navigation') {
          const newUrl = event.data.url
          if (newUrl && newUrl !== currentUrl) {
            // 在新标签页中打开链接，而不是当前页导航
            if (onNavigateToNewTab) {
              onNavigateToNewTab(newUrl)
            } else {
              // 如果没有提供回调，则在当前页导航（向后兼容）
              setCurrentUrl(newUrl)
              handleLoadStart()
            }
          }
        }
      }
    }

    window.addEventListener('message', handleMessage)
    return () => window.removeEventListener('message', handleMessage)
  }, [currentUrl, onNavigateToNewTab])

  // 注入脚本到iframe中监听链接点击
  const injectNavigationScript = () => {
    if (iframeRef.current?.contentWindow) {
      try {
        const script = `
          (function() {
            // 监听所有链接点击
            document.addEventListener('click', function(e) {
              const link = e.target.closest('a');
              if (link && link.href) {
                e.preventDefault();
                // 发送消息给父窗口
                window.parent.postMessage({
                  type: 'navigation',
                  url: link.href
                }, '*');
              }
            });
          })();
        `

        const iframe = iframeRef.current
        const doc = iframe.contentDocument || iframe.contentWindow?.document
        if (doc) {
          const scriptElement = doc.createElement('script')
          scriptElement.textContent = script
          doc.head.appendChild(scriptElement)
        }
      } catch (error) {
        // 跨域限制，无法注入脚本
        console.log('无法注入导航脚本，可能存在跨域限制')
      }
    }
  }

  // 移除预检查，让所有网站都先尝试iframe加载

  return (
    <div className="h-full flex flex-col">
      {/* MVP版本：移除工具栏和地址栏，专注内容展示 */}

      {/* 加载状态 */}
      {loading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center z-10">
          <div className="text-center">
            <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm text-gray-600">正在加载网页...</p>
            <p className="text-xs text-gray-500 mt-1">AI分析将在后台进行</p>
          </div>
        </div>
      )}

      {/* 错误状态 */}
      {error && (
        <div className="flex-1 flex items-center justify-center bg-gray-50">
          <div className="text-center p-8">
            <AlertCircle size={48} className="mx-auto text-red-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <div className="flex flex-wrap justify-center gap-2">
              <button
                onClick={handleRefresh}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                重试
              </button>
              {browserMode === 'iframe' && (
                <button
                  onClick={() => switchBrowserMode('proxy')}
                  className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  尝试代理模式
                </button>
              )}
              {browserMode !== 'render' && (
                <button
                  onClick={() => switchBrowserMode('render')}
                  className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                >
                  尝试渲染模式
                </button>
              )}
              <button
                onClick={handleOpenExternal}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                在新窗口打开
              </button>
            </div>
          </div>
        </div>
      )}

      {/* 浏览器内容 */}
      {!error && (
        <div className="flex-1 relative">
          {/* 模式状态指示器 */}
          {browserMode !== 'iframe' && (
            <div className="absolute top-2 left-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs z-10">
              {browserMode === 'proxy' ? '代理模式' : '渲染模式'}
            </div>
          )}

          <iframe
            ref={iframeRef}
            src={getDisplayUrl()}
            className="w-full h-full border-0"
            onLoad={handleLoad}
            onError={handleError}
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups allow-popups-to-escape-sandbox allow-top-navigation"
            title={title}
            loading="eager"
            style={{
              backgroundColor: '#ffffff',
              overflow: 'auto'
            }}
          />
        </div>
      )}

      {/* 性能监控器 */}
      {showPerformanceMonitor && (
        <PerformanceMonitor
          url={currentUrl}
          onMetricsUpdate={(metrics) => {
            console.log('性能指标更新:', metrics)
          }}
        />
      )}
    </div>
  )
}

export default LightBrowser
