'use client'

import React from 'react'

interface DynamicBackgroundProps {
  className?: string
}

const DynamicBackground: React.FC<DynamicBackgroundProps> = ({ className = '' }) => {
  return (
    <>
      {/* 动态背景 */}
      <div className={`bg-animation ${className}`}>
        <div className="gradient-orb"></div>
        <div className="gradient-orb"></div>
        <div className="gradient-orb"></div>
      </div>
      
      {/* 网格背景 */}
      <div className="grid-bg"></div>
    </>
  )
}

export default DynamicBackground
