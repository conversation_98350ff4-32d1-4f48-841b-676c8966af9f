'use client'

import React, { Component, ReactNode } from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'

interface ErrorBoundaryState {
  hasError: boolean
  error?: Error
}

interface ErrorBoundaryProps {
  children: ReactNode
  fallback?: ReactNode
}

class MarkdownErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Markdown rendering error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <div className="text-red-500 text-sm p-2 bg-red-50 rounded border">
          <p>内容渲染出错</p>
          <details className="mt-1">
            <summary className="cursor-pointer text-xs">错误详情</summary>
            <pre className="text-xs mt-1 whitespace-pre-wrap">
              {this.state.error?.message}
            </pre>
          </details>
        </div>
      )
    }

    return this.props.children
  }
}

interface SafeMarkdownProps {
  children: string
  className?: string
}

const SafeMarkdown: React.FC<SafeMarkdownProps> = ({ children, className }) => {
  // 验证和清理内容
  const cleanContent = React.useMemo(() => {
    if (!children || typeof children !== 'string') {
      return '内容为空'
    }

    let content = children

    // 如果内容看起来像JSON，尝试提取实际内容
    if (content.trim().startsWith('{') && content.trim().endsWith('}')) {
      try {
        const parsed = JSON.parse(content)
        if (parsed.content && typeof parsed.content === 'string') {
          content = parsed.content
        }
      } catch (e) {
        // 如果解析失败，继续使用原始内容
        console.warn('JSON解析失败，使用原始内容:', e)
      }
    }

    // 移除可能导致问题的控制字符
    const cleaned = content.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')

    // 限制内容长度，防止过长内容导致性能问题
    if (cleaned.length > 10000) {
      return cleaned.substring(0, 10000) + '\n\n...(内容过长，已截断)'
    }

    return cleaned
  }, [children])

  return (
    <MarkdownErrorBoundary
      fallback={
        <div className="text-gray-500 text-sm p-2 bg-gray-50 rounded border">
          <p>内容渲染失败，显示原始文本：</p>
          <pre className="mt-1 whitespace-pre-wrap text-xs">{cleanContent}</pre>
        </div>
      }
    >
      <div className={className}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            // 自定义代码块渲染
            code: ({ children, className, ...props }: any) => {
              const isInline = !className?.includes('language-')
              if (isInline) {
                return (
                  <code
                    className="px-2 py-0.5 bg-blue-50 text-blue-700 rounded-md text-sm font-mono border border-blue-200/50 !bg-blue-50 !text-blue-700"
                    style={{
                      backgroundColor: '#eff6ff !important',
                      color: '#1d4ed8 !important',
                      padding: '0.125rem 0.5rem',
                      borderRadius: '0.375rem',
                      fontSize: '0.875rem',
                      fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                      border: '1px solid #bfdbfe'
                    }}
                    {...props}
                  >
                    {children}
                  </code>
                )
              }
              return (
                <pre
                  className="bg-slate-900 text-slate-100 rounded-xl p-4 overflow-x-auto border border-slate-700 my-4"
                  style={{
                    backgroundColor: '#0f172a !important',
                    color: '#f1f5f9 !important',
                    padding: '1rem',
                    borderRadius: '0.75rem',
                    overflowX: 'auto',
                    border: '1px solid #334155',
                    margin: '1rem 0'
                  }}
                >
                  <code className="text-sm font-mono" {...props}>
                    {children}
                  </code>
                </pre>
              )
            },
            // 自定义表格渲染
            table: ({ children }) => (
              <div className="overflow-x-auto my-6">
                <table className="min-w-full border-collapse border border-slate-300 rounded-lg overflow-hidden shadow-sm">
                  {children}
                </table>
              </div>
            ),
            th: ({ children }) => (
              <th className="border border-slate-300 bg-slate-100 px-4 py-3 text-left font-semibold text-slate-800">
                {children}
              </th>
            ),
            td: ({ children }) => (
              <td className="border border-slate-300 px-4 py-3 text-slate-700">
                {children}
              </td>
            ),
            // 自定义引用块渲染
            blockquote: ({ children }) => (
              <blockquote
                className="border-l-4 border-blue-400 bg-gradient-to-r from-blue-50/80 to-transparent pl-6 py-4 my-6 rounded-r-lg shadow-sm"
                style={{
                  borderLeft: '4px solid #60a5fa',
                  background: 'linear-gradient(to right, rgba(239, 246, 255, 0.8), transparent)',
                  paddingLeft: '1.5rem',
                  paddingTop: '1rem',
                  paddingBottom: '1rem',
                  marginTop: '1.5rem',
                  marginBottom: '1.5rem',
                  borderRadius: '0 0.5rem 0.5rem 0',
                  boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)'
                }}
              >
                <div
                  className="text-slate-700 italic"
                  style={{
                    color: '#334155',
                    fontStyle: 'italic'
                  }}
                >
                  {children}
                </div>
              </blockquote>
            ),
            // 自定义标题渲染
            h1: ({ children }) => (
              <h1
                className="text-2xl font-bold text-slate-800 mb-4 pb-2 border-b-2 border-slate-200"
                style={{
                  fontSize: '1.5rem',
                  fontWeight: '700',
                  color: '#1e293b',
                  marginBottom: '1rem',
                  paddingBottom: '0.5rem',
                  borderBottom: '2px solid #e2e8f0',
                  marginTop: '1.5rem'
                }}
              >
                {children}
              </h1>
            ),
            h2: ({ children }) => (
              <h2
                className="text-xl font-semibold text-slate-800 mb-3 mt-6 pb-1 border-b border-slate-200"
                style={{
                  fontSize: '1.25rem',
                  fontWeight: '600',
                  color: '#1e293b',
                  marginBottom: '0.75rem',
                  marginTop: '1.5rem',
                  paddingBottom: '0.25rem',
                  borderBottom: '1px solid #e2e8f0'
                }}
              >
                {children}
              </h2>
            ),
            h3: ({ children }) => (
              <h3
                className="text-lg font-semibold text-slate-800 mb-2 mt-5"
                style={{
                  fontSize: '1.125rem',
                  fontWeight: '600',
                  color: '#1e293b',
                  marginBottom: '0.5rem',
                  marginTop: '1.25rem'
                }}
              >
                {children}
              </h3>
            ),
            // 自定义列表渲染
            ul: ({ children }) => (
              <ul
                className="space-y-1 my-3 pl-6 list-disc"
                style={{
                  marginTop: '0.75rem',
                  marginBottom: '0.75rem',
                  paddingLeft: '1.5rem',
                  listStyleType: 'disc'
                }}
              >
                {children}
              </ul>
            ),
            ol: ({ children }) => (
              <ol
                className="space-y-1 my-3 pl-6 list-decimal"
                style={{
                  marginTop: '0.75rem',
                  marginBottom: '0.75rem',
                  paddingLeft: '1.5rem',
                  listStyleType: 'decimal'
                }}
              >
                {children}
              </ol>
            ),
            li: ({ children }) => (
              <li
                className="text-slate-700 leading-relaxed marker:text-slate-400"
                style={{
                  color: '#334155',
                  lineHeight: '1.625',
                  marginBottom: '0.25rem'
                }}
              >
                {children}
              </li>
            ),
            // 自定义段落渲染
            p: ({ children }) => (
              <p
                className="text-slate-700 leading-relaxed my-3"
                style={{
                  color: '#334155',
                  lineHeight: '1.625',
                  marginTop: '0.75rem',
                  marginBottom: '0.75rem'
                }}
              >
                {children}
              </p>
            ),
          }}
        >
          {cleanContent}
        </ReactMarkdown>
      </div>
    </MarkdownErrorBoundary>
  )
}

export default SafeMarkdown
