'use client'

import React from 'react'
import { cn } from '@/lib/utils'

interface ModernLoaderProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton'
  text?: string
  className?: string
}

const ModernLoader: React.FC<ModernLoaderProps> = ({
  size = 'md',
  variant = 'spinner',
  text,
  className
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  }

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  }

  if (variant === 'spinner') {
    return (
      <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
        <div className={cn(
          'border-2 border-gray-200 border-t-blue-500 rounded-full animate-spin',
          sizeClasses[size]
        )} />
        {text && (
          <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  if (variant === 'dots') {
    return (
      <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
        <div className="flex space-x-1">
          <div className={cn(
            'bg-blue-500 rounded-full dot-flashing',
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )} />
          <div className={cn(
            'bg-blue-500 rounded-full dot-flashing',
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )} />
          <div className={cn(
            'bg-blue-500 rounded-full dot-flashing',
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )} />
        </div>
        {text && (
          <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  if (variant === 'pulse') {
    return (
      <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
        <div className={cn(
          'bg-gradient-to-r from-blue-400 to-purple-500 rounded-full animate-pulse',
          sizeClasses[size]
        )} />
        {text && (
          <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  if (variant === 'skeleton') {
    return (
      <div className={cn('space-y-3', className)}>
        <div className="skeleton h-4 w-3/4" />
        <div className="skeleton h-4 w-1/2" />
        <div className="skeleton h-4 w-5/6" />
        {text && (
          <p className={cn('text-gray-600 font-medium mt-4', textSizeClasses[size])}>
            {text}
          </p>
        )}
      </div>
    )
  }

  return null
}

export default ModernLoader
