'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Send, User, Bot } from 'lucide-react'

interface ChatMessage {
  role: 'user' | 'assistant'
  content: string
  timestamp?: number
}

interface AIChatProps {
  messages: ChatMessage[]
  loading: boolean
  onSendMessage: (message: string) => void
}

function MessageBubble({ message }: { message: ChatMessage }) {
  const isUser = message.role === 'user'
  
  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-2`}>
        <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
          isUser ? 'bg-primary text-primary-foreground ml-2' : 'bg-muted text-muted-foreground mr-2'
        }`}>
          {isUser ? <User className="w-4 h-4" /> : <Bot className="w-4 h-4" />}
        </div>
        <div className={`rounded-2xl px-4 py-3 ${
          isUser
            ? 'bg-primary text-primary-foreground rounded-tr-sm'
            : 'bg-muted text-muted-foreground rounded-tl-sm border border-border'
        }`}>
          <p className="text-sm leading-relaxed whitespace-pre-wrap">
            {message.content}
          </p>
        </div>
      </div>
    </div>
  )
}

function TypingIndicator() {
  return (
    <div className="flex justify-start mb-4">
      <div className="flex items-start space-x-2">
        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center mr-2">
          <Bot className="w-4 h-4" />
        </div>
        <div className="bg-muted rounded-2xl rounded-tl-sm px-4 py-3 border border-border">
          <div className="flex space-x-1">
            <div className="w-2 h-2 bg-muted-foreground/50 rounded-full dot-flashing"></div>
            <div className="w-2 h-2 bg-muted-foreground/50 rounded-full dot-flashing"></div>
            <div className="w-2 h-2 bg-muted-foreground/50 rounded-full dot-flashing"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

export function AIChat({ messages, loading, onSendMessage }: AIChatProps) {
  const [inputValue, setInputValue] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages, loading])

  const handleSubmit = () => {
    if (inputValue.trim() && !loading) {
      onSendMessage(inputValue.trim())
      setInputValue('')
      // 重置 textarea 高度
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto'
      }
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSubmit()
    }
  }

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto'
      textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 120) + 'px'
    }
  }

  if (messages.length === 0 && !loading) {
    return (
      <div className="h-full flex flex-col">
        {/* 空状态 */}
        <div className="flex-1 flex flex-col items-center justify-center space-y-4">
          <div className="text-4xl text-muted-foreground/30">🤖</div>
          <div className="text-center">
            <p className="font-medium text-muted-foreground">AI 助手准备就绪</p>
            <p className="text-sm text-muted-foreground/60 mt-2">
              基于文章内容回答您的问题
            </p>
          </div>
        </div>

        {/* 输入框 */}
        <div className="border-t border-border bg-background p-4">
          <div className="flex items-end space-x-3">
            <Textarea
              ref={textareaRef}
              placeholder="询问关于这篇文章的问题..."
              value={inputValue}
              onChange={(e) => {
                setInputValue(e.target.value)
                adjustTextareaHeight()
              }}
              onKeyPress={handleKeyPress}
              disabled={loading}
              className="min-h-[44px] max-h-[120px] resize-none scrollbar-thin"
              rows={1}
            />
            <Button
              onClick={handleSubmit}
              disabled={!inputValue.trim() || loading}
              size="sm"
              className="h-11 w-11 p-0 flex-shrink-0 transition-all duration-200 hover:scale-105 active:scale-95"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 消息列表 */}
      <div className="flex-1 overflow-y-auto scrollbar-thin p-4 space-y-2">
        {messages.map((message, index) => (
          <MessageBubble key={index} message={message} />
        ))}
        {loading && <TypingIndicator />}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入框 */}
      <div className="border-t border-border bg-background p-4">
        <div className="flex items-end space-x-3">
          <Textarea
            ref={textareaRef}
            placeholder="继续提问..."
            value={inputValue}
            onChange={(e) => {
              setInputValue(e.target.value)
              adjustTextareaHeight()
            }}
            onKeyPress={handleKeyPress}
            disabled={loading}
            className="min-h-[44px] max-h-[120px] resize-none scrollbar-thin"
            rows={1}
          />
          <Button
            onClick={handleSubmit}
            disabled={!inputValue.trim() || loading}
            size="sm"
            className="h-11 w-11 p-0 flex-shrink-0 transition-all duration-200 hover:scale-105 active:scale-95"
          >
            <Send className="w-4 h-4" />
          </Button>
        </div>
      </div>
    </div>
  )
} 