好的，遵命。

我们将整合之前所有的讨论、决策、优化和最终的澄清，撰写一份**史诗级的、足够详细的、可以直接作为您个人开发圣经的最终版PRD**。

这份文档将包含所有必要的模块，从宏观愿景到微观的技术实现细节，旨在消除您在开发过程中的任何模糊地带，确保最终产品与我们共同构想的蓝图完全一致。

---
---

### **产品需求文档 (PRD): 「沉淀」MVP**

**产品名称:** 沉淀 (Precipitation)
**版本:** **v_MVP_FINAL_EXECUTION** (最终执行版)
**日期:** 2025年6月10日
**产品负责人:** (您的名字)

---

#### **1. 文档修订历史**

| 版本号 | 修订日期 | 修订人 | 修订说明 |
| :--- | :--- | :--- | :--- |
| v1.0 | 2025/06/08 | (您的名字) | 初稿创建，定义基本概念。 |
| v2.0 | 2025/06/09 | (您的名字) | 引入“AI伴读笔记”概念，明确核心体验。 |
| v3.0 | 2025/06/10 | (您的名字) | 引入“IDE式布局”和“标签页”系统，明确产品形态。 |
| **FINAL**| 2025/06/10 | (您的名字) | **最终澄清：** 明确AI输出物为**一份**唯一的、整体的笔记，重构交互流程与数据模型。此版本为开发基准。 |

---

#### **2. 产品概述与愿景 (Overview & Vision)**

*   **产品定位:** 「沉淀」是一款专为信息过载的普通人设计的“AI记忆沉淀器”。它不是一个需要用户主动维护的笔记软件，而是一个自动化的知识处理与存储系统。
*   **核心理念:** 用户输入的每一份原文（网页/文本），都由AI提炼成**一份**统一的、结构化的**“AI笔记”**。用户无需手动拆分或整理，只需对这份AI生成的完整洞察做出“保存”或“忽略”的决策。
*   **愿景:** 让知识的获取、理解和沉淀过程合而为一，为用户构建一个“无需管理”的个人知识库。

---

#### **3. 目标用户与核心痛点 (Target Audience & Pain Points)**

*   **用户画像:** 广泛的知识工作者、学生、研究员，以及任何有大量阅读需求，但又苦于手动整理笔记、信息回顾困难的用户。他们是“信息快消费”和“知识焦虑”并存的群体。
*   **核心痛点:**
    1.  **记不住:** 阅读量大，但有效信息留存率低。
    2.  **找不到:** 知道自己看过某内容，但无法快速定位和回溯。
    3.  **不想整:** 对Notion/Obsidian等工具的学习成本和维护负担望而却步。
    4.  **核心诉求:** “我只管看，你帮我记，我需要时能找到。”

---

#### **4. 产品流程图 (User Flow Diagram)**

```mermaid
graph TD
    A[打开「沉淀」] --> B(启动页: 输入URL/文本);
    B --> C{处理内容};
    C --> D[主工作区: 打开新标签页, 渲染原文];
    C --> E[AI助手: 生成一份AI笔记];
    
    subgraph "核心交互循环"
        D <--> E;
        E --> F[AI对话窗口: 上下文问答];
    end

    E --> G{决策};
    G -- 点击“存入知识库” --> H[API: 保存当前会话到本地DB];
    G -- 关闭标签页 --> I[忽略, 数据不保存];
    
    J[顶部导航: 点击“知识库”] --> K[知识库页面: 展示历史列表];
    K --> L{选择历史记录};
    L --> D;
    
    K --> M[点击“同步到FastGPT”];
    M --> N[API: 推送所有已保存会话到FastGPT];
    K --> O[点击“与知识库对话”];
    O --> P[新浏览器Tab: 打开FastGPT对话链接];

```

---

#### **5. 功能详述与技术规格 (Features & Technical Specifications)**

**史诗 1: 核心工作台 (The Core Workbench)**

> **目标:** 构建一个流畅、高效的阅读与AI分析环境。

| 需求ID | 需求描述 | UI/UX 设计与交互 | 技术实现细节 |
| :--- | :--- | :--- | :--- |
| **F-1.1**| **应用布局** | 采用**三栏式IDE布局**。左右边栏宽度可调，中间主工作区占据核心。**MVP阶段，左侧边栏不开发。** | **前端:** 使用`react-split-pane`或类似库实现可拖动调整宽度的布局。 |
| **F-1.2**| **主工作区：标签页系统** | - 支持打开新标签页，关闭标签页，点击切换激活的标签页。<br>- 标签页标题自动填充为原文标题。<br>- 无标签页时，显示居中的输入框。 | **前端:** 使用`Ant Design Tabs`或类似组件库快速实现。管理一个包含所有打开的标签页信息的状态数组。 |
| **F-1.3**| **AI助手面板** | - 位于界面右侧。<br>- 其内容（AI笔记、对话）与主工作区当前激活的标签页**严格绑定**。切换标签页时，此面板内容必须瞬时更新。 | **前端:** 使用React Context或状态管理库（如Zustand），将`activeTabId`作为全局状态，右侧面板根据此ID来渲染对应数据。 |
| **F-1.4**| **输入与处理流程** | - 用户在输入框粘贴URL/文本后按回车。<br>- 主工作区和AI助手面板同时显示加载状态（骨架屏/Spinner）。 | **前端:** `onSubmit`事件触发API调用。<br>**后端:** 创建`POST /api/process`接口，内置网页抓取 (`readability`) 和AI调用 (`openai`) 模块。 |
| **F-1.5**| **内容渲染** | - **原文 (主工作区):** 必须有优美的、统一的阅读排版。<br>- **AI笔记 (右侧面板):** 以Markdown格式清晰展示。 | **前端:** 原文渲染区应用`@tailwindcss/typography`插件。AI笔记区使用`react-markdown`组件。 |
| **F-1.6**| **AI对话功能** | - 位于AI助手面板下方。<br>- 包含对话历史和输入框。<br>- 对话历史可滚动。 | **前后端:** 创建`POST /api/chat`接口，接收`prompt`和`context`（原文+AI笔记）。前端实现聊天界面，支持流式输出打字机效果。 |

**史诗 2: 知识沉淀与调用 (Archiving & Recalling)**

> **目标:** 提供一个极简但完整的知识保存、回顾与调用闭环。

| 需求ID | 需求描述 | UI/UX 设计与交互 | 技术实现细节 |
| :--- | :--- | :--- | :--- |
| **F-2.1**| **“存入知识库”功能** | - 在右侧AI助手面板顶部，提供一个清晰的**「存入知识库」**按钮。<br>- 点击后，按钮变为“已保存 ✓”的禁用状态，并有短暂的成功Toast提示。 | **前后端:** 前端调用`POST /api/archive/create`，将当前会话的完整数据发送给后端。后端使用Prisma将数据写入SQLite。 |
| **F-2.2**| **知识库入口** | - 在应用顶部导航栏，提供一个名为**「知识库」**的链接/按钮。 | **前端:** 使用Next.js的`<Link>`组件，指向`/archive`页面。 |
| **F-2.3**| **知识库页面** | - 一个独立的、极简的页面。<br>- 以**列表形式**，按时间倒序展示所有已保存的会话标题。<br>- 每个列表项都是可点击的。 | **前端:** 创建`/app/archive/page.js`。页面加载时调用`GET /api/archive/list`获取数据并渲染。 |
| **F-2.4**| **加载历史会话** | - 在知识库页面，点击任一历史标题。<br>- 应用将**清空当前所有标签页**，然后打开一个新标签页，恢复所选历史会话的“原文+AI笔记”场景。 | **前端:** 点击列表项时，调用`GET /api/archive/:id`获取完整数据，然后更新主工作区的状态。 |
| **F-2.5**| **FastGPT集成** | - 在知识库页面顶部，提供**「同步到FastGPT」**和**「与知识库对话」**两个按钮。<br>- “同步”按钮有明确的加载和状态反馈。 | **前端:** “同步”按钮调用`POST /api/sync/fastgpt`。“对话”按钮使用`window.open(url, '_blank')`在新浏览器标签页打开FastGPT链接。 |

---

#### **6. 数据模型 (Data Schema)**

*   **数据库:** SQLite
*   **ORM:** Prisma
*   **核心模型 `Session`:**
    ```prisma
    model Session {
      id                 String   @id @default(cuid())
      title              String   // 从原文<title>或URL生成
      sourceType         String   // "url" or "text"
      sourceData         String   // URL链接或原文的标识
      originalContent    String   // 抓取并清洗后的原文HTML或文本
      aiNoteMarkdown     String   // AI生成的、唯一的、结构化的Markdown笔记
      createdAt          DateTime @default(now())
    }
    ```

---

#### **7. 非功能性需求 & MVP范围之外**

*   **非功能性需求:** 性能（10秒内响应）、兼容性（主流现代浏览器）、易用性（零教程上手）。
*   **MVP范围之外:** PDF/音视频处理、高级交互（双向链接/图谱）、用户账户系统、协同分享、原生客户端。

---

#### **8. 上线标准 (Release Criteria)**

1.  本文档中所有`F-`开头的需求均已开发完成并通过手动测试。
2.  核心用户流程图中的所有路径均已跑通，无阻塞性BUG。
3.  应用已成功部署，并通过公网URL可稳定访问。

---

这份PRD提供了从宏观到微观的完整视图，旨在成为您开发过程中的可靠伙伴。祝您编码愉快，顺利将「沉淀」带到这个世界！