# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

「沉淀」是一个AI记忆沉淀器，专为信息过载的知识工作者设计。它能够自动提取网页内容，生成结构化的AI笔记，并提供基于内容的智能问答功能。

## 核心架构

### 技术栈
- **前端**: Next.js 15.3.3 (App Router) + React 19 + TypeScript 5
- **UI**: Tailwind CSS 2.2.9 (CDN) + Font Awesome 6.5.1 (CDN) + shadcn/ui
- **状态管理**: Zustand 5.0.5
- **数据库**: SQLite + Prisma 6.9.0
- **AI**: OpenAI GPT-4 + FastGPT 集成
- **内容处理**: @mozilla/readability + Playwright + jsdom

### 双模式架构
1. **浏览模式** (`browse`): 主要的内容处理和阅读模式，IDE风格三栏布局
2. **反思模式** (`reflection`): 知识库管理和深度思考

### 三栏布局系统
- **左栏**: 侧边栏，管理多标签页
- **中栏**: 主工作区，显示原文或AI笔记
- **右栏**: AI助手面板，支持流式对话

## 开发命令

```bash
# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 启动生产服务器
npm start

# 代码检查
npm run lint

# 数据库操作
npx prisma generate    # 生成 Prisma Client
npx prisma db push     # 推送 schema 变更到数据库
npx prisma studio      # 打开数据库管理界面
```

## 测试方法

```bash
# 测试 OpenAI API 连接
node test-connection.js

# 功能测试（使用测试 HTML 文件）
# 手动打开 test-*.html 文件进行前端测试
```

## 核心功能流程

### 内容处理流程
1. 用户输入 URL → API 调用 `/api/process-url`
2. 后端使用 readability/playwright 提取内容
3. 调用 OpenAI 生成结构化笔记（包含摘要、大纲、要点等）
4. 返回原文和 AI 笔记到前端显示

### AI 对话流程
1. 用户在助手面板提问 → API 调用 `/api/chat/stream`
2. 将原文、AI笔记、历史对话作为上下文
3. 流式返回 AI 响应，支持 Markdown 渲染

### 知识库管理
1. 用户点击"存入知识库" → 保存到本地 SQLite
2. 支持 FastGPT 同步 → 调用 `/api/fastgpt/sync`
3. 知识库页面提供搜索、管理功能

## 关键设计原则

1. **极简设计**: 避免过度工程化，保持界面简洁
2. **性能优先**: 使用虚拟滚动、流式处理、缓存优化
3. **用户体验**: 快速响应、流畅交互、清晰反馈
4. **本地优先**: 数据存储在本地 SQLite，保护用户隐私

## 注意事项

- **使用 CDN 版本**的 Tailwind CSS 和 Font Awesome（已在全局配置）
- **遵循现有代码风格**，查看相邻文件了解约定
- **不要创建不必要的文件**，优先编辑现有文件
- **保持代码简洁**，避免过度抽象和复杂设计
- **重视错误处理**，提供清晰的用户反馈

## 环境变量

创建 `.env.local` 文件并配置：
```env
OPENAI_API_KEY="your_openai_api_key"
DATABASE_URL="file:./prisma/dev.db"
FASTGPT_API_URL="your_fastgpt_url"  # 可选
```

## 数据模型

- **Card**: 知识卡片，存储原文和AI笔记
- **SavedNote**: 保存的笔记，包含完整对话历史
- 支持 FastGPT 同步状态追踪

## Memory-OS IDE 设计规范

本项目正在向 Memory-OS IDE 演进，设计原则：
- 左树-中卡-右助手的核心交互模式
- 支持拖拽文件自动生成节点
- 知识卡片默认展示，可切换查看原文
- AI助手支持引用卡片并跳转定位