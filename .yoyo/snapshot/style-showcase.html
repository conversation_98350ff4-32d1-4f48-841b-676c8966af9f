<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识卡片项目 - 样式展示</title>
    <link href="/tailwind.min.css" rel="stylesheet">
    <style>
        /* 引入项目的全局样式变量 */
        :root {
            --radius: 0.75rem;
            --bg-primary: #f8fafc;
            --bg-secondary: #ffffff;
            --bg-glass: rgba(255, 255, 255, 0.7);
            --bg-glass-strong: rgba(255, 255, 255, 0.85);
            --bg-glass-subtle: rgba(255, 255, 255, 0.5);
            --background: #FFFFFF;
            --foreground: #0F172A;
            --primary: #6366F1;
            --primary-foreground: #FFFFFF;
            --accent-light: #818cf8;
            --border: rgba(226, 232, 240, 0.5);
            --border-strong: #E2E8F0;
            --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03);
            --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.05), 0 10px 10px -5px rgba(0, 0, 0, 0.02);
            --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.08);
            --shadow-glass: 0 8px 32px rgba(99, 102, 241, 0.1);
            --blur: 20px;
            --blur-subtle: 10px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Microsoft YaHei', 'Inter', sans-serif;
            background: var(--bg-primary);
            color: var(--foreground);
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* 背景装饰系统 */
        .bg-decoration {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: -1;
            overflow: hidden;
            pointer-events: none;
        }

        .bg-circle {
            position: absolute;
            border-radius: 50%;
            filter: blur(100px);
            opacity: 0.4;
            animation: float 20s infinite ease-in-out;
        }

        .bg-circle:nth-child(1) {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            top: -200px;
            right: -100px;
        }

        .bg-circle:nth-child(2) {
            width: 300px;
            height: 300px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            bottom: -150px;
            left: -50px;
            animation-delay: -5s;
        }

        .bg-circle:nth-child(3) {
            width: 250px;
            height: 250px;
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0) scale(1); }
            25% { transform: translate(-30px, 30px) scale(1.05); }
            50% { transform: translate(30px, -30px) scale(0.95); }
            75% { transform: translate(-20px, -20px) scale(1.02); }
        }

        /* 毛玻璃效果 */
        .glass-effect {
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur));
            -webkit-backdrop-filter: blur(var(--blur));
            border: 1px solid var(--border);
        }

        .glass-effect-strong {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(var(--blur));
            -webkit-backdrop-filter: blur(var(--blur));
            border: 1px solid var(--border);
        }

        /* 现代化卡片 */
        .modern-card {
            background: var(--bg-glass);
            backdrop-filter: blur(var(--blur));
            -webkit-backdrop-filter: blur(var(--blur));
            border: 1px solid var(--border);
            border-radius: 16px;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .modern-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
            border-color: var(--primary);
        }

        .modern-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.3), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .modern-card:hover::before {
            opacity: 1;
        }

        /* 现代化输入框 */
        .modern-input {
            background: var(--bg-glass-strong);
            backdrop-filter: blur(var(--blur-subtle));
            -webkit-backdrop-filter: blur(var(--blur-subtle));
            border: 1px solid var(--border);
            transition: all 0.3s ease;
        }

        .modern-input:focus {
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
            background: var(--bg-secondary);
            outline: none;
        }
    </style>
</head>
<body>
    <!-- 背景装饰 -->
    <div class="bg-decoration">
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
        <div class="bg-circle"></div>
    </div>

    <div class="min-h-screen p-8">
        <div class="max-w-6xl mx-auto">
            <!-- 标题区域 -->
            <div class="text-center mb-12">
                <h1 class="text-4xl font-bold text-gray-900 mb-4">知识卡片项目样式展示</h1>
                <p class="text-lg text-gray-600">现代化毛玻璃效果与Bento Grid布局设计</p>
            </div>

            <!-- 毛玻璃效果展示 -->
            <div class="mb-12">
                <h2 class="text-2xl font-semibold mb-6">毛玻璃效果展示</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="glass-effect p-6 rounded-2xl">
                        <h3 class="font-semibold mb-2">标准毛玻璃</h3>
                        <p class="text-sm text-gray-600">适用于一般背景元素</p>
                    </div>
                    <div class="glass-effect-strong p-6 rounded-2xl">
                        <h3 class="font-semibold mb-2">强化毛玻璃</h3>
                        <p class="text-sm text-gray-600">适用于重要内容区域</p>
                    </div>
                    <div class="modern-card p-6">
                        <h3 class="font-semibold mb-2">现代化卡片</h3>
                        <p class="text-sm text-gray-600">带hover效果的交互卡片</p>
                    </div>
                </div>
            </div>

            <!-- 现代化输入框展示 -->
            <div class="mb-12">
                <h2 class="text-2xl font-semibold mb-6">现代化输入框</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium mb-2">标准输入框</label>
                        <input type="text" class="modern-input w-full px-4 py-3 rounded-xl" placeholder="输入内容...">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">文本区域</label>
                        <textarea class="modern-input w-full px-4 py-3 rounded-xl h-24 resize-none" placeholder="输入多行内容..."></textarea>
                    </div>
                </div>
            </div>

            <!-- 知识卡片样式展示 -->
            <div class="mb-12">
                <h2 class="text-2xl font-semibold mb-6">知识卡片样式</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="modern-card p-6 group">
                        <div class="flex items-start justify-between mb-4">
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">React 18 新特性</h3>
                            <button class="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <p class="text-sm text-gray-600 mb-4">React 18 带来了许多激动人心的新特性，包括并发渲染、自动批处理、Suspense 改进等...</p>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">保存</button>
                    </div>

                    <div class="modern-card p-6 group">
                        <div class="flex items-start justify-between mb-4">
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">设计系统构建</h3>
                            <button class="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <p class="text-sm text-gray-600 mb-4">探索如何从零开始构建一个完整的设计系统，包括设计原则、组件规范、设计令牌等...</p>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">保存</button>
                    </div>

                    <div class="modern-card p-6 group">
                        <div class="flex items-start justify-between mb-4">
                            <h3 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">用户体验设计</h3>
                            <button class="opacity-0 group-hover:opacity-100 transition-opacity text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                </svg>
                            </button>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-blue-500/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                        <p class="text-sm text-gray-600 mb-4">深入了解各种用户研究方法，包括访谈、问卷调查、可用性测试等...</p>
                        <button class="px-4 py-2 bg-blue-600 text-white rounded-lg text-sm hover:bg-blue-700 transition-colors">保存</button>
                    </div>
                </div>
            </div>

            <!-- 总结 -->
            <div class="glass-effect-strong p-8 rounded-2xl text-center">
                <h2 class="text-2xl font-semibold mb-4">样式优化完成</h2>
                <p class="text-gray-600 mb-6">
                    已成功实现现代化毛玻璃效果、背景装饰动画、优雅的卡片设计和响应式布局。
                    所有样式改进都严格遵循了仅修改CSS和样式的约束，保持了功能完整性。
                </p>
                <div class="flex justify-center space-x-4">
                    <span class="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm">✓ 毛玻璃效果</span>
                    <span class="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm">✓ 背景装饰</span>
                    <span class="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm">✓ 现代化卡片</span>
                    <span class="px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm">✓ 响应式设计</span>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
