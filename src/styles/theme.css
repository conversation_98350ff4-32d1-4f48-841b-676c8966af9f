/* 沉淀视觉风格 - 蓝绿青低饱和 + 无边界设计 */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

:root {
  /* 主色系 - 柔和蓝绿青 */
  --primary: #2A9D8F;
  --primary-light: #A2D5CE;
  --primary-dark: #1C6F64;
  --accent: #56C3B5;
  
  /* 中性色 - 极简无边 */
  --neutral-100: #F8FAF9;
  --neutral-200: #EEF2F1;
  --neutral-400: #C7D1CF;
  --neutral-600: #6C7977;
  --neutral-900: #29302E;
  
  /* 状态色 - 低饱和 */
  --success: #4BBF9B;
  --warning: #FFC46D;
  --error: #EF767A;
  
  /* 软阴影 */
  --shadow-soft: 0 4px 16px rgba(42, 157, 143, 0.07);
  --shadow-float: 0 8px 32px rgba(42, 157, 143, 0.12);
  
  /* 玻璃态效果 */
  --glass-light: rgba(255, 255, 255, 0.6);
  --glass-dark: rgba(16, 19, 18, 0.5);
  --glass-blur: blur(12px);
  
  /* 间距规范 */
  --gutter: 24px;
  --gutter-lg: 32px;
  --spacing-sm: 16px;
  --spacing-md: 20px;
  
  /* 动效时间 */
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
}

/* 暗色主题 */
[data-theme="dark"] {
  --primary: #35B0A0;
  --neutral-100: #101312;
  --neutral-200: #1A1E1C;
  --neutral-400: #3A4340;
  --neutral-600: #8A9289;
  --neutral-900: #E8F0EF;
  --glass-light: var(--glass-dark);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--neutral-100);
  color: var(--neutral-900);
  font-size: 16px;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 无边界卡片 */
.card-borderless {
  background: white;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-soft);
  border: none;
}

/* 玻璃态浮窗 */
.glass-morphism {
  background: var(--glass-light);
  backdrop-filter: var(--glass-blur);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 1rem;
}

/* 软分割线 */
.divider-soft {
  height: 1px;
  background: var(--neutral-200);
  border: none;
}

/* 悬浮动效 */
.hover-lift {
  transition: transform var(--duration-fast) var(--ease-out);
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* 微交互动效系统 */

/* 生成按钮动效 */
.generate-button {
  transition: all var(--duration-fast) var(--ease-out);
  position: relative;
  overflow: hidden;
}

.generate-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-float);
}

.generate-button:active {
  transform: scale(0.98);
}

.generate-button.generating {
  animation: generateSpin 2s linear infinite;
}

@keyframes generateSpin {
  0% { 
    transform: rotate(0deg) scale(1.05);
    background: var(--primary);
  }
  25% { 
    background: var(--accent);
  }
  50% { 
    transform: rotate(240deg) scale(1.05);
    background: var(--primary);
  }
  75% { 
    background: var(--accent);
  }
  100% { 
    transform: rotate(480deg) scale(1.05);
    background: var(--primary);
  }
}

/* 点击波纹效果 */
.generate-button::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transition: width 0.3s, height 0.3s, opacity 0.3s;
  transform: translate(-50%, -50%);
  opacity: 0;
}

.generate-button:active::before {
  width: 300px;
  height: 300px;
  opacity: 1;
  transition: width 0s, height 0s, opacity 0.3s;
}

/* 浮现动效 */
.float-in {
  animation: floatIn var(--duration-normal) var(--ease-out);
}

@keyframes floatIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 状态点跳动 */
.status-dot {
  animation: statusPulse 2s ease-in-out infinite;
}

@keyframes statusPulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.2);
  }
}

/* 卡片悬浮增强 */
.card-hover {
  transition: all var(--duration-fast) var(--ease-out);
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-float);
}

/* 按钮组切换动效 */
.button-group {
  position: relative;
}

.button-group::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: all var(--duration-fast) var(--ease-out);
}

.button-group .active::after {
  width: 100%;
}

/* 进度条动效 */
.progress-bar {
  position: relative;
  background: var(--neutral-200);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  border-radius: 4px;
  transition: width var(--duration-normal) var(--ease-out);
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* 通知弹出动效 */
.notification {
  animation: notificationSlide var(--duration-normal) var(--ease-out);
}

@keyframes notificationSlide {
  from {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

/* 搜索框聚焦动效 */
.search-input {
  position: relative;
  transition: all var(--duration-fast) var(--ease-out);
}

.search-input:focus-within {
  transform: scale(1.02);
}

.search-input::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--primary);
  transition: all var(--duration-fast) var(--ease-out);
  transform: translateX(-50%);
}

.search-input:focus-within::after {
  width: 100%;
}

/* 加载骨架屏增强 */
.skeleton {
  background: linear-gradient(90deg, var(--neutral-200), var(--neutral-100), var(--neutral-200));
  background-size: 200% 100%;
  animation: skeletonLoading 1.5s ease-in-out infinite;
}

@keyframes skeletonLoading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 标签页切换动效 */
.tab-content {
  animation: tabFadeIn var(--duration-fast) var(--ease-out);
}

@keyframes tabFadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动指示器 */
.scroll-indicator {
  position: fixed;
  top: 0;
  left: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary), var(--accent));
  transform-origin: left;
  z-index: 1000;
  transition: transform var(--duration-fast) var(--ease-out);
}

/* 文字闪烁输入效果 */
.typing-effect::after {
  content: '|';
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 选区工具条 */
.selection-toolbar {
  animation: fadeInUp var(--duration-fast) var(--ease-out);
  transform-origin: center bottom;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* diff 高亮动效 */
.diff-highlight {
  background-color: rgba(162, 213, 206, 0.2);
  transition: background-color 3s var(--ease-out);
}

.diff-highlight.fade-out {
  background-color: transparent;
}

/* 减弱动效 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .generate-button.generating {
    animation: none;
  }
}

/* 可访问性 - 焦点样式 */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: 0.25rem;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-200);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-400);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-600);
}