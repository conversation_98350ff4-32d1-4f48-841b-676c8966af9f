import { JSDOM } from 'jsdom'
import crypto from 'crypto'

export interface OutlineItem {
  id: string
  title: string
  level: number
  offset: number
  children?: OutlineItem[]
}

/**
 * 从HTML内容中提取文档大纲
 */
export function extractDocumentOutline(htmlContent: string): OutlineItem[] {
  const dom = new JSDOM(htmlContent)
  const document = dom.window.document
  
  // 查找所有标题元素
  const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
  
  if (headings.length === 0) {
    // 如果没有标题，尝试通过段落分析生成大纲
    return extractOutlineFromParagraphs(document)
  }
  
  const outline: OutlineItem[] = []
  const stack: { item: OutlineItem; level: number }[] = []
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1))
    const title = heading.textContent?.trim() || `章节 ${index + 1}`
    const id = heading.id || generateId(title)
    
    // 确保元素有ID，用于导航
    if (!heading.id) {
      heading.id = id
    }
    
    const item: OutlineItem = {
      id,
      title,
      level,
      offset: getElementOffset(heading as HTMLElement),
      children: []
    }
    
    // 找到合适的父级
    while (stack.length > 0 && stack[stack.length - 1].level >= level) {
      stack.pop()
    }
    
    if (stack.length === 0) {
      // 顶级项
      outline.push(item)
    } else {
      // 子项
      const parent = stack[stack.length - 1].item
      if (!parent.children) parent.children = []
      parent.children.push(item)
    }
    
    stack.push({ item, level })
  })
  
  return outline
}

/**
 * 从段落内容中智能提取大纲
 */
function extractOutlineFromParagraphs(document: Document): OutlineItem[] {
  const paragraphs = document.querySelectorAll('p')
  const outline: OutlineItem[] = []
  
  // 分析段落，找出可能的章节标题
  let sectionCount = 0
  
  paragraphs.forEach((p, index) => {
    const text = p.textContent?.trim() || ''
    
    // 启发式规则：识别可能的章节标题
    if (isLikelyTitle(text, index, paragraphs.length)) {
      sectionCount++
      const id = generateId(`section-${sectionCount}`)
      
      // 给段落添加ID
      p.id = id
      
      outline.push({
        id,
        title: text.length > 50 ? text.substring(0, 50) + '...' : text,
        level: 1,
        offset: getElementOffset(p as HTMLElement)
      })
    }
  })
  
  // 如果没有找到合适的标题，按内容长度分段
  if (outline.length === 0) {
    const totalLength = document.body.textContent?.length || 0
    const sectionsNeeded = Math.min(5, Math.ceil(totalLength / 1000))
    
    for (let i = 0; i < sectionsNeeded; i++) {
      outline.push({
        id: `section-${i + 1}`,
        title: `第 ${i + 1} 部分`,
        level: 1,
        offset: (totalLength / sectionsNeeded) * i
      })
    }
  }
  
  return outline
}

/**
 * 判断文本是否可能是标题
 */
function isLikelyTitle(text: string, index: number, total: number): boolean {
  // 空文本不是标题
  if (!text || text.length === 0) return false
  
  // 太长的文本不太可能是标题
  if (text.length > 100) return false
  
  // 检查是否包含标题特征
  const titlePatterns = [
    /^第[一二三四五六七八九十\d]+[章节部分]/,
    /^[一二三四五六七八九十\d]+[、.]\s/,
    /^Chapter\s+\d+/i,
    /^Section\s+\d+/i,
    /^Part\s+\d+/i,
  ]
  
  for (const pattern of titlePatterns) {
    if (pattern.test(text)) return true
  }
  
  // 检查是否是短句且位置合适
  if (text.length < 50 && text.split(/\s+/).length < 10) {
    // 位于开头或接近段落分界的短句可能是标题
    if (index === 0 || index % Math.ceil(total / 5) === 0) {
      return true
    }
  }
  
  return false
}

/**
 * 获取元素在文档中的偏移位置
 */
function getElementOffset(element: HTMLElement): number {
  // 在服务端环境中，我们使用文本位置作为偏移量
  let offset = 0
  let currentNode = element.previousSibling
  
  while (currentNode) {
    if (currentNode.textContent) {
      offset += currentNode.textContent.length
    }
    currentNode = currentNode.previousSibling
  }
  
  return offset
}

/**
 * 生成唯一ID
 */
function generateId(text: string): string {
  const hash = crypto.createHash('md5').update(text).digest('hex')
  return `outline-${hash.substring(0, 8)}`
}