'use client'

import React, { useEffect, useState } from 'react'
import { <PERSON><PERSON>, MessageSquare, Edit3, <PERSON>rk<PERSON> } from 'lucide-react'
import { cn } from '@/lib/utils'

interface SelectionToolbarProps {
  selectedText: string
  position: { x: number; y: number }
  context: 'original' | 'notes'
  onAction: (action: 'copy' | 'ask' | 'edit') => void
  onClose: () => void
}

const SelectionToolbar: React.FC<SelectionToolbarProps> = ({
  selectedText,
  position,
  context,
  onAction,
  onClose
}) => {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    // 延迟显示，避免误触
    const timer = setTimeout(() => setIsVisible(true), 120)
    return () => clearTimeout(timer)
  }, [])

  // 计算工具条位置
  const calculatePosition = () => {
    const toolbarWidth = 200
    const toolbarHeight = 48
    const padding = 10

    let x = position.x - toolbarWidth / 2
    let y = position.y - toolbarHeight - padding

    // 边界检查
    x = Math.max(padding, Math.min(x, window.innerWidth - toolbarWidth - padding))
    y = Math.max(padding, y)

    return { x, y }
  }

  const { x, y } = calculatePosition()

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(selectedText)
      onAction('copy')
      onClose()
    } catch (error) {
      console.error('复制失败:', error)
    }
  }

  const handleAsk = () => {
    onAction('ask')
  }

  const handleEdit = () => {
    onAction('edit')
  }

  if (!isVisible) return null

  return (
    <div
      className={cn(
        "fixed z-50 selection-toolbar glass-morphism",
        "flex items-center space-x-1 px-2 py-1"
      )}
      style={{
        left: `${x}px`,
        top: `${y}px`,
        backgroundColor: 'var(--glass-light)',
        backdropFilter: 'var(--glass-blur)',
        border: '1px solid rgba(255, 255, 255, 0.3)',
        boxShadow: 'var(--shadow-float)'
      }}
    >
      {/* 复制按钮 */}
      <button
        onClick={handleCopy}
        className="p-2 rounded transition-all hover-lift"
        style={{ color: 'var(--neutral-600)' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
          e.currentTarget.style.color = 'var(--primary)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent'
          e.currentTarget.style.color = 'var(--neutral-600)'
        }}
        title="复制"
      >
        <Copy className="w-4 h-4" />
      </button>

      {/* 分隔线 */}
      <div 
        className="w-px h-6 divider-soft"
        style={{ backgroundColor: 'var(--neutral-400)' }}
      />

      {/* AI 问答按钮 */}
      <button
        onClick={handleAsk}
        className="p-2 rounded transition-all hover-lift"
        style={{ color: 'var(--accent)' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--primary-light)'
          e.currentTarget.style.color = 'var(--primary-dark)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent'
          e.currentTarget.style.color = 'var(--accent)'
        }}
        title={context === 'original' ? '询问原文' : '询问笔记'}
      >
        <MessageSquare className="w-4 h-4" />
      </button>

      {/* AI 编辑按钮（仅在笔记区域显示） */}
      {context === 'notes' && (
        <>
          <div 
            className="w-px h-6 divider-soft"
            style={{ backgroundColor: 'var(--neutral-400)' }}
          />
          <button
            onClick={handleEdit}
            className="p-2 rounded transition-all hover-lift"
            style={{ color: 'var(--accent)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--primary-light)'
              e.currentTarget.style.color = 'var(--primary-dark)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'var(--accent)'
            }}
            title="AI 编辑"
          >
            <Edit3 className="w-4 h-4" />
          </button>
        </>
      )}

      {/* 魔法按钮 */}
      <div 
        className="w-px h-6 divider-soft"
        style={{ backgroundColor: 'var(--neutral-400)' }}
      />
      <button
        onClick={() => onAction('ask')}
        className="p-2 rounded transition-all hover-lift"
        style={{ color: 'var(--accent)' }}
        onMouseEnter={(e) => {
          e.currentTarget.style.backgroundColor = 'var(--primary-light)'
          e.currentTarget.style.color = 'var(--primary-dark)'
          e.currentTarget.style.transform = 'scale(1.1)'
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.backgroundColor = 'transparent'
          e.currentTarget.style.color = 'var(--accent)'
          e.currentTarget.style.transform = 'scale(1)'
        }}
        title="AI 助手"
      >
        <Sparkles className="w-4 h-4" />
      </button>

      {/* 三角箭头指示器 */}
      <div
        className="absolute top-full left-1/2 transform -translate-x-1/2"
        style={{
          width: 0,
          height: 0,
          borderLeft: '6px solid transparent',
          borderRight: '6px solid transparent',
          borderTop: '6px solid var(--glass-light)',
          filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1))'
        }}
      />
    </div>
  )
}

export default SelectionToolbar