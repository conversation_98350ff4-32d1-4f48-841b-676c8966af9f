'use client'

import React, { useState, useCallback, useRef, useEffect } from 'react'
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Check } from 'lucide-react'
import SafeMarkdown from '../ui/SafeMarkdown'
import { cn } from '@/lib/utils'
import { useAppStore } from '@/lib/store'

interface StructuredNotesProps {
  content: string
  isGenerating: boolean
  onClose: () => void
}

const StructuredNotes: React.FC<StructuredNotesProps> = ({
  content,
  isGenerating,
  onClose
}) => {
  const [isSaved, setIsSaved] = useState(false)
  const [selectedText, setSelectedText] = useState('')
  const contentRef = useRef<HTMLDivElement>(null)
  const activeTab = useAppStore(state => 
    state.tabs.find(tab => tab.id === state.activeTabId)
  )

  // 处理文本选择
  const handleTextSelection = useCallback(() => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText(selection.toString())
    } else {
      setSelectedText('')
    }
  }, [])

  // 保存到知识库
  const handleSave = useCallback(async () => {
    if (!activeTab || isSaved) return

    try {
      const response = await fetch('/api/notes/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: activeTab.title,
          originalContent: activeTab.originalContent,
          structuredNotes: content,
          integratedNotes: content, // 初始时相同
          sourceType: activeTab.sourceType,
          sourceData: activeTab.sourceData,
          tags: []
        }),
      })

      if (response.ok) {
        setIsSaved(true)
        // 3秒后重置状态
        setTimeout(() => setIsSaved(false), 3000)
      }
    } catch (error) {
      console.error('保存失败:', error)
    }
  }, [activeTab, content, isSaved])

  // 生成动画效果
  useEffect(() => {
    if (isGenerating && contentRef.current) {
      // 添加生成动画类
      contentRef.current.classList.add('animate-pulse')
    } else {
      contentRef.current?.classList.remove('animate-pulse')
    }
  }, [isGenerating])

  return (
    <div className="h-full flex flex-col glass-morphism">
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-4 border-b divider-soft"
        style={{ 
          backgroundColor: 'var(--glass-light)',
          backdropFilter: 'var(--glass-blur)'
        }}
      >
        <div className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5" style={{ color: 'var(--accent)' }} />
          <h3 className="font-medium" style={{ color: 'var(--neutral-900)' }}>AI 结构化笔记</h3>
          {isGenerating && (
            <div className="flex items-center space-x-1 text-sm" style={{ color: 'var(--neutral-600)' }}>
              <div className="w-1 h-1 rounded-full animate-bounce" style={{ backgroundColor: 'var(--accent)' }} />
              <div className="w-1 h-1 rounded-full animate-bounce delay-100" style={{ backgroundColor: 'var(--accent)' }} />
              <div className="w-1 h-1 rounded-full animate-bounce delay-200" style={{ backgroundColor: 'var(--accent)' }} />
              <span>生成中</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={handleSave}
            disabled={isGenerating || isSaved || !content}
            className={cn(
              "flex items-center space-x-1 px-3 py-1.5 rounded-lg transition-all duration-200 hover-lift",
              "disabled:cursor-not-allowed disabled:opacity-50"
            )}
            style={{
              backgroundColor: isSaved ? 'var(--success)' : 'var(--primary)',
              color: 'white'
            }}
          >
            {isSaved ? (
              <>
                <Check className="w-4 h-4" />
                <span className="text-sm">已保存</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span className="text-sm">存入知识库</span>
              </>
            )}
          </button>
          
          <button
            onClick={onClose}
            className="p-1 rounded transition-colors hover-lift"
            style={{ color: 'var(--neutral-600)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 内容区域 */}
      <div 
        ref={contentRef}
        className="flex-1 overflow-y-auto"
        style={{ 
          padding: 'var(--gutter)',
          backgroundColor: 'white'
        }}
        onMouseUp={handleTextSelection}
      >
        {isGenerating ? (
          <div className="space-y-4">
            {/* 生成中的骨架屏 */}
            <div className="h-8 rounded animate-pulse" style={{ backgroundColor: 'var(--neutral-200)' }} />
            <div className="space-y-2">
              <div className="h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--neutral-200)' }} />
              <div className="h-4 rounded animate-pulse w-5/6" style={{ backgroundColor: 'var(--neutral-200)' }} />
              <div className="h-4 rounded animate-pulse w-4/6" style={{ backgroundColor: 'var(--neutral-200)' }} />
            </div>
            <div className="space-y-2">
              <div className="h-4 rounded animate-pulse" style={{ backgroundColor: 'var(--neutral-200)' }} />
              <div className="h-4 rounded animate-pulse w-5/6" style={{ backgroundColor: 'var(--neutral-200)' }} />
            </div>
          </div>
        ) : content ? (
          <div className="prose prose-lg max-w-none" style={{ color: 'var(--neutral-900)' }}>
            <SafeMarkdown>{content}</SafeMarkdown>
          </div>
        ) : (
          <div className="flex items-center justify-center h-full" style={{ color: 'var(--neutral-400)' }}>
            <div className="text-center">
              <Sparkles className="w-12 h-12 mx-auto mb-4" />
              <p>正在准备生成笔记...</p>
            </div>
          </div>
        )}
      </div>

      {/* 底部状态栏 */}
      <div 
        className="px-4 py-2 border-t divider-soft"
        style={{ backgroundColor: 'var(--neutral-200)' }}
      >
        <div className="flex items-center justify-between text-xs" style={{ color: 'var(--neutral-600)' }}>
          <div>
            {selectedText && (
              <span>已选中 {selectedText.length} 个字符</span>
            )}
          </div>
          <div className="flex items-center space-x-4">
            <span>字数：{content.length}</span>
            <span>右键唤出AI编辑助手</span>
          </div>
        </div>
      </div>
    </div>
  )
}

export default StructuredNotes