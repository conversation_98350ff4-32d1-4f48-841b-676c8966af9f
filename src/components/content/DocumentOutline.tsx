'use client'

import React, { useState, useEffect } from 'react'
import { ChevronRight, ChevronDown, Hash, FileText } from 'lucide-react'
import { cn } from '@/lib/utils'

interface OutlineItem {
  id: string
  title: string
  level: number
  children?: OutlineItem[]
  isActive?: boolean
}

interface DocumentOutlineProps {
  outline: OutlineItem[]
  onNavigate: (sectionId: string) => void
}

const DocumentOutline: React.FC<DocumentOutlineProps> = ({ outline, onNavigate }) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set())
  const [activeSection, setActiveSection] = useState<string | null>(null)
  const [isCollapsed, setIsCollapsed] = useState(false)

  // 监听滚动，更新当前活跃章节
  useEffect(() => {
    const handleScroll = () => {
      const sections = outline.map(item => ({
        id: item.id,
        element: document.getElementById(item.id)
      })).filter(item => item.element)

      const scrollPosition = window.scrollY + 100 // 偏移量

      let currentSection = null
      for (const section of sections) {
        if (section.element && section.element.offsetTop <= scrollPosition) {
          currentSection = section.id
        }
      }

      setActiveSection(currentSection)
    }

    window.addEventListener('scroll', handleScroll)
    handleScroll() // 初始调用
    
    return () => window.removeEventListener('scroll', handleScroll)
  }, [outline])

  // 渲染大纲项
  const renderOutlineItem = (item: OutlineItem, depth: number = 0) => {
    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.has(item.id)
    const isActive = activeSection === item.id

    return (
      <div key={item.id} className="select-none">
        <div
          className={cn(
            "flex items-center px-3 py-2 cursor-pointer rounded-lg transition-all duration-200 hover-lift",
            depth > 0 && "ml-4"
          )}
          style={{
            backgroundColor: isActive ? 'var(--primary-light)' : 'transparent',
            color: isActive ? 'var(--primary-dark)' : 'var(--neutral-600)',
            fontWeight: isActive ? '600' : '400',
            borderLeft: isActive ? '4px solid var(--primary)' : 'none',
            paddingLeft: isActive ? '12px' : '16px'
          }}
          onClick={() => {
            onNavigate(item.id)
            if (hasChildren) {
              setExpandedItems(prev => {
                const next = new Set(prev)
                if (next.has(item.id)) {
                  next.delete(item.id)
                } else {
                  next.add(item.id)
                }
                return next
              })
            }
          }}
          onMouseEnter={(e) => {
            if (!isActive) {
              e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
              e.currentTarget.style.color = 'var(--primary)'
            }
          }}
          onMouseLeave={(e) => {
            if (!isActive) {
              e.currentTarget.style.backgroundColor = 'transparent'
              e.currentTarget.style.color = 'var(--neutral-600)'
            }
          }}
        >
          {hasChildren ? (
            isExpanded ? (
              <ChevronDown className="w-3 h-3 mr-2 flex-shrink-0" />
            ) : (
              <ChevronRight className="w-3 h-3 mr-2 flex-shrink-0" />
            )
          ) : (
            <Hash className="w-3 h-3 mr-2 flex-shrink-0" style={{ color: 'var(--neutral-400)' }} />
          )}
          <span className="text-sm truncate" title={item.title}>
            {item.title}
          </span>
        </div>
        
        {hasChildren && isExpanded && (
          <div className="mt-1">
            {item.children!.map(child => renderOutlineItem(child, depth + 1))}
          </div>
        )}
      </div>
    )
  }

  if (isCollapsed) {
    return (
      <div 
        className="w-12 flex flex-col items-center py-4 border-r divider-soft"
        style={{ backgroundColor: 'var(--neutral-100)' }}
      >
        <button
          onClick={() => setIsCollapsed(false)}
          className="p-2 rounded transition-colors hover-lift"
          title="展开大纲"
          style={{ color: 'var(--neutral-600)' }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
          }}
        >
          <FileText className="w-5 h-5" />
        </button>
      </div>
    )
  }

  return (
    <div 
      className="w-64 border-r divider-soft flex flex-col h-full"
      style={{ backgroundColor: 'var(--neutral-100)' }}
    >
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-4 border-b divider-soft"
        style={{ backgroundColor: 'var(--neutral-100)' }}
      >
        <h3 className="font-medium" style={{ color: 'var(--neutral-900)' }}>文档大纲</h3>
        <button
          onClick={() => setIsCollapsed(true)}
          className="p-1 rounded transition-colors hover-lift"
          style={{ color: 'var(--neutral-600)' }}
          onMouseEnter={(e) => {
            e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.backgroundColor = 'transparent'
          }}
        >
          <ChevronRight className="w-4 h-4" />
        </button>
      </div>

      {/* 大纲内容 */}
      <div className="flex-1 overflow-y-auto" style={{ padding: 'var(--spacing-sm)' }}>
        {outline.length === 0 ? (
          <div className="text-center py-8" style={{ color: 'var(--neutral-400)' }}>
            <FileText className="w-8 h-8 mx-auto mb-2" />
            <p className="text-sm">暂无大纲</p>
          </div>
        ) : (
          <div className="space-y-1">
            {outline.map(item => renderOutlineItem(item))}
          </div>
        )}
      </div>

      {/* 底部提示 */}
      <div className="p-3 border-t divider-soft">
        <p className="text-xs text-center" style={{ color: 'var(--neutral-400)' }}>
          点击跳转 · 自动跟随
        </p>
      </div>
    </div>
  )
}

export default DocumentOutline