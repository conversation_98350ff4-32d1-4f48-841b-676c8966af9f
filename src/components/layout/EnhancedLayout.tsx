'use client'

import React, { useState, useRef, useCallback, useEffect } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Plus, BookOpen, Sparkles, ChevronRight } from 'lucide-react'
import WorkArea from './WorkArea'
import DynamicBackground from '../ui/DynamicBackground'
import FloatingAIAssistant from '../ai/FloatingAIAssistant'
import { cn } from '@/lib/utils'
import DocumentOutline from '../content/DocumentOutline'
import StructuredNotes from '../content/StructuredNotes'

// 新的增强布局组件
const EnhancedLayout: React.FC = () => {
  const { 
    tabs, 
    middlePanelWidth,
    setMiddlePanelWidth
  } = useAppStore()
  
  const [showStructuredNotes, setShowStructuredNotes] = useState(false)
  const [notesGenerating, setNotesGenerating] = useState(false)
  const [selectedText, setSelectedText] = useState<{text: string, context: 'original' | 'notes'} | null>(null)
  const [aiAssistantPosition, setAIAssistantPosition] = useState<{x: number, y: number} | null>(null)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef<HTMLDivElement>(null)

  // 获取当前活跃标签页
  const activeTab = tabs.find(tab => tab.id === useAppStore.getState().activeTabId)
  const isEmptyNewTab = activeTab &&
    activeTab.sourceData === '' &&
    activeTab.originalContent === '' &&
    activeTab.aiNoteMarkdown === ''

  // 处理中间分隔条拖拽
  const handleDividerMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault()
    setIsDragging(true)
  }, [])

  // 处理拖拽过程
  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!containerRef.current || !isDragging) return

    const containerRect = containerRef.current.getBoundingClientRect()
    const mouseX = ((e.clientX - containerRect.left) / containerRect.width) * 100

    // 限制中栏宽度范围：40% - 70%
    const newMiddleWidth = Math.max(40, Math.min(70, mouseX))
    setMiddlePanelWidth(newMiddleWidth)
  }, [isDragging, setMiddlePanelWidth])

  // 处理拖拽结束
  const handleMouseUp = useCallback(() => {
    setIsDragging(false)
  }, [])

  // 处理文本选择
  const handleTextSelection = useCallback((context: 'original' | 'notes') => {
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText({
        text: selection.toString(),
        context
      })
    }
  }, [])

  // 处理右键或快捷键唤出AI助手
  const handleContextMenu = useCallback((e: React.MouseEvent, context: 'original' | 'notes') => {
    e.preventDefault()
    const selection = window.getSelection()
    if (selection && selection.toString().trim()) {
      setSelectedText({
        text: selection.toString(),
        context
      })
      setAIAssistantPosition({
        x: e.clientX,
        y: e.clientY
      })
    } else {
      // 即使没有选中文本，也可以唤出AI助手
      setAIAssistantPosition({
        x: e.clientX,
        y: e.clientY
      })
    }
  }, [])

  // 一键生成结构化笔记
  const handleGenerateNotes = useCallback(async () => {
    if (!activeTab || activeTab.aiNoteMarkdown) return
    
    setNotesGenerating(true)
    setShowStructuredNotes(true)
    
    // 调用API生成结构化笔记
    try {
      const response = await fetch('/api/process-with-mapping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(
          activeTab.sourceType === 'url' 
            ? { url: activeTab.sourceData } 
            : { text: activeTab.originalContent }
        ),
      })

      if (response.ok) {
        const data = await response.json()
        useAppStore.getState().updateTab(activeTab.id, {
          aiNoteMarkdown: data.aiNote,
          documentOutline: data.documentOutline // 新增：文档大纲数据
        })
      }
    } catch (error) {
      console.error('生成笔记失败:', error)
    } finally {
      setNotesGenerating(false)
    }
  }, [activeTab])

  // 添加全局鼠标事件监听
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'col-resize'
      document.body.style.userSelect = 'none'
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    }
  }, [isDragging, handleMouseMove, handleMouseUp])

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K 唤出AI助手
      if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault()
        const rect = containerRef.current?.getBoundingClientRect()
        if (rect) {
          setAIAssistantPosition({
            x: rect.width / 2,
            y: rect.height / 2
          })
        }
      }

      // Cmd/Ctrl + G 生成笔记
      if ((e.ctrlKey || e.metaKey) && e.key === 'g') {
        e.preventDefault()
        if (!activeTab?.aiNoteMarkdown) {
          handleGenerateNotes()
        }
      }

      // Esc 关闭浮动面板
      if (e.key === 'Escape') {
        setAIAssistantPosition(null)
        setSelectedText(null)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [activeTab, handleGenerateNotes])

  return (
    <div className="h-screen flex flex-col relative" style={{ backgroundColor: 'var(--neutral-100)' }}>
      {/* 动态背景 - 只在没有标签页或空标签页时显示 */}
      {(tabs.length === 0 || isEmptyNewTab) && <DynamicBackground />}

      {/* 动态标签栏 - 只在有标签页时显示 */}
      {tabs.length > 0 && (
        <div className="flex items-center px-6 py-3 border-b divider-soft flex-shrink-0" style={{ backgroundColor: 'var(--neutral-100)' }}>
          <div className="flex items-center">
            <div className="flex items-center overflow-x-auto">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  className={cn(
                    "flex items-center space-x-3 px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 flex-shrink-0 hover-lift",
                    tab.id === useAppStore.getState().activeTabId
                      ? 'card-borderless text-primary font-medium border-l-4 border-primary/50'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white/60'
                  )}
                  onClick={() => useAppStore.getState().setActiveTab(tab.id)}
                  style={{ 
                    color: tab.id === useAppStore.getState().activeTabId ? 'var(--primary)' : 'var(--neutral-600)',
                    backgroundColor: tab.id === useAppStore.getState().activeTabId ? 'white' : 'transparent'
                  }}
                >
                  <span className="text-sm font-medium truncate max-w-32">
                    {tab.title}
                  </span>
                  {tab.isLoading && (
                    <div className="w-4 h-4 border-2 border-t-transparent rounded-full animate-spin" style={{ borderColor: 'var(--primary)' }}></div>
                  )}
                  {tab.aiAnalyzing && (
                    <div className="w-2 h-2 rounded-full animate-pulse" style={{ backgroundColor: 'var(--accent)' }}></div>
                  )}
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      useAppStore.getState().removeTab(tab.id)
                    }}
                    className="p-1 hover:bg-gray-200/80 rounded-full transition-colors"
                  >
                    <X size={14} />
                  </button>
                </div>
              ))}
            </div>

            <button
              onClick={() => {
                useAppStore.getState().addTab({
                  title: '新标签页',
                  sourceType: 'text',
                  sourceData: '',
                  originalContent: '',
                  aiNoteMarkdown: '',
                  isLoading: false
                })
              }}
              className="ml-1 p-2 hover:bg-white/60 rounded-lg transition-all duration-200 flex-shrink-0 hover-lift"
              title="新建标签页"
            >
              <Plus size={14} style={{ color: 'var(--neutral-600)' }} />
            </button>
          </div>
        </div>
      )}

      {/* 主要内容区域 - 新的布局 */}
      <div
        ref={containerRef}
        className="flex-1 flex overflow-hidden min-h-0"
      >
        {/* 左栏 - 原文 + 大纲导航 */}
        {tabs.length > 0 && !isEmptyNewTab && (
          <div 
            className="flex card-borderless border-r divider-soft"
            style={{ 
              width: `${middlePanelWidth}%`,
              backgroundColor: 'white'
            }}
            onContextMenu={(e) => handleContextMenu(e, 'original')}
            onMouseUp={() => handleTextSelection('original')}
          >
            {/* 文档大纲 */}
            <DocumentOutline 
              outline={activeTab?.documentOutline || []}
              onNavigate={(sectionId) => {
                // 滚动到对应章节
                const element = document.getElementById(sectionId)
                element?.scrollIntoView({ behavior: 'smooth' })
              }}
            />
            
            {/* 原文内容 */}
            <div className="flex-1 overflow-y-auto" style={{ padding: 'var(--gutter)' }}>
              <WorkArea />
            </div>
          </div>
        )}

        {/* 中间区域 - 生成笔记按钮或分隔条 */}
        <div className="relative flex items-center justify-center" style={{ width: '120px' }}>
          {activeTab && !activeTab.aiNoteMarkdown && !showStructuredNotes ? (
            <button
              onClick={handleGenerateNotes}
              disabled={notesGenerating}
              className={cn(
                "absolute z-10 px-4 py-2 rounded-full transition-all generate-button",
                "text-white font-medium flex items-center space-x-2",
                notesGenerating && "generating"
              )}
              style={{
                backgroundColor: 'var(--primary)',
                boxShadow: 'var(--shadow-float)'
              }}
            >
              <Sparkles className="w-4 h-4" />
              <span className="text-sm font-medium">
                {notesGenerating ? '生成中...' : '生成笔记'}
              </span>
            </button>
          ) : showStructuredNotes && (
            <div
              className={cn(
                "group flex-shrink-0 relative cursor-col-resize transition-all duration-150",
                isDragging && "shadow-md"
              )}
              onMouseDown={handleDividerMouseDown}
              style={{
                width: isDragging ? '6px' : '4px',
                margin: '0 2px'
              }}
            >
              <div className="absolute inset-y-0 left-1/2 transform -translate-x-1/2 w-1 flex items-center justify-center">
                <div 
                  className={cn(
                    "w-0.5 h-12 rounded-full transition-all duration-150",
                    isDragging ? "h-16" : "group-hover:h-16"
                  )}
                  style={{
                    background: `linear-gradient(to bottom, var(--primary), var(--accent))`
                  }}
                />
              </div>
            </div>
          )}
        </div>

        {/* 右栏 - 结构化笔记（可选显示） */}
        {showStructuredNotes && activeTab && (
          <div 
            className="flex-1 overflow-hidden"
            style={{ backgroundColor: 'var(--neutral-200)' }}
            onContextMenu={(e) => handleContextMenu(e, 'notes')}
            onMouseUp={() => handleTextSelection('notes')}
          >
            <StructuredNotes 
              content={activeTab.aiNoteMarkdown}
              isGenerating={notesGenerating}
              onClose={() => setShowStructuredNotes(false)}
            />
          </div>
        )}

        {/* 如果没有内容，显示欢迎界面 */}
        {(tabs.length === 0 || isEmptyNewTab) && (
          <div className="flex-1">
            <WorkArea />
          </div>
        )}
      </div>

      {/* 浮动AI助手 */}
      {aiAssistantPosition && (
        <FloatingAIAssistant
          position={aiAssistantPosition}
          selectedText={selectedText}
          context={aiAssistantPosition.x < window.innerWidth / 2 ? 'original' : 'notes'}
          onClose={() => {
            setAIAssistantPosition(null)
            setSelectedText(null)
          }}
        />
      )}

      {/* 快捷键提示 */}
      <div className="fixed bottom-4 right-4 text-xs space-y-1" style={{ color: 'var(--neutral-400)' }}>
        <div>⌘K 唤出AI助手</div>
        <div>⌘G 生成笔记</div>
        <div>右键 上下文AI</div>
      </div>
    </div>
  )
}

export default EnhancedLayout