'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useAppStore } from '@/lib/store'
import { X, Send, Sparkles, Edit3, MessageSquare } from 'lucide-react'
import { cn } from '@/lib/utils'
import SafeMarkdown from '../ui/SafeMarkdown'

interface FloatingAIAssistantProps {
  position: { x: number; y: number }
  selectedText: { text: string; context: 'original' | 'notes' } | null
  context: 'original' | 'notes'
  onClose: () => void
}

const FloatingAIAssistant: React.FC<FloatingAIAssistantProps> = ({
  position,
  selectedText,
  context,
  onClose
}) => {
  const activeTab = useAppStore(state => 
    state.tabs.find(tab => tab.id === state.activeTabId)
  )
  const [input, setInput] = useState('')
  const [messages, setMessages] = useState<Array<{
    role: 'user' | 'assistant'
    content: string
    modifications?: Array<{
      type: 'insert' | 'replace' | 'delete'
      content: string
      reason: string
    }>
  }>>([])
  const [isLoading, setIsLoading] = useState(false)
  const [mode, setMode] = useState<'ask' | 'agent'>(context === 'notes' ? 'agent' : 'ask')
  const containerRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)

  // 自动聚焦输入框
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // 处理点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [onClose])

  // 计算面板位置，避免超出视窗
  const calculatePosition = () => {
    const padding = 20
    const panelWidth = 400
    const panelHeight = 500

    let x = position.x
    let y = position.y

    // 确保不超出右边界
    if (x + panelWidth > window.innerWidth - padding) {
      x = window.innerWidth - panelWidth - padding
    }

    // 确保不超出下边界
    if (y + panelHeight > window.innerHeight - padding) {
      y = window.innerHeight - panelHeight - padding
    }

    // 确保不超出左边界和上边界
    x = Math.max(padding, x)
    y = Math.max(padding, y)

    return { x, y }
  }

  const { x, y } = calculatePosition()

  // 发送消息
  const handleSend = useCallback(async () => {
    if (!input.trim() || !activeTab || isLoading) return

    const userMessage = input.trim()
    setInput('')
    setIsLoading(true)

    // 添加用户消息
    setMessages(prev => [...prev, { role: 'user', content: userMessage }])

    try {
      // 构建上下文
      const contextData = {
        mode,
        message: userMessage,
        selectedText: selectedText?.text,
        context: {
          originalContent: activeTab.originalContent,
          aiNote: activeTab.aiNoteMarkdown,
          focusArea: context
        },
        chatHistory: messages.map(m => ({
          role: m.role,
          content: m.content
        }))
      }

      const response = await fetch('/api/chat-with-document', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(contextData),
      })

      if (!response.ok) throw new Error('Failed to get response')

      const reader = response.body?.getReader()
      if (!reader) throw new Error('No reader available')

      const decoder = new TextDecoder()
      let assistantMessage = ''
      let modifications: any[] = []

      // 添加空的助手消息占位符
      setMessages(prev => [...prev, { role: 'assistant', content: '', modifications: [] }])

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              
              if (parsed.type === 'content') {
                assistantMessage += parsed.content
                setMessages(prev => {
                  const newMessages = [...prev]
                  newMessages[newMessages.length - 1] = {
                    ...newMessages[newMessages.length - 1],
                    content: assistantMessage
                  }
                  return newMessages
                })
              } else if (parsed.type === 'modification' && mode === 'agent') {
                modifications.push(parsed.modification)
                // 实时应用修改到笔记
                applyModification(parsed.modification)
              }
            } catch (e) {
              console.error('Parse error:', e)
            }
          }
        }
      }

      // 更新最终的修改记录
      if (modifications.length > 0) {
        setMessages(prev => {
          const newMessages = [...prev]
          newMessages[newMessages.length - 1].modifications = modifications
          return newMessages
        })
      }
    } catch (error) {
      console.error('Chat error:', error)
      setMessages(prev => [...prev, {
        role: 'assistant',
        content: '抱歉，我遇到了一些问题。请稍后重试。'
      }])
    } finally {
      setIsLoading(false)
    }
  }, [input, activeTab, isLoading, mode, selectedText, context, messages])

  // 应用修改到笔记
  const applyModification = (modification: any) => {
    if (!activeTab) return

    // 这里实现实时修改笔记的逻辑
    // 可以使用diff算法来优雅地显示变更
    const currentNote = activeTab.aiNoteMarkdown
    let newNote = currentNote

    switch (modification.type) {
      case 'insert':
        // 插入内容
        newNote = currentNote + '\n\n' + modification.content
        break
      case 'replace':
        // 替换内容
        if (modification.target) {
          newNote = currentNote.replace(modification.target, modification.content)
        }
        break
      case 'delete':
        // 删除内容
        if (modification.target) {
          newNote = currentNote.replace(modification.target, '')
        }
        break
    }

    useAppStore.getState().updateTab(activeTab.id, {
      aiNoteMarkdown: newNote
    })
  }

  return (
    <div
      ref={containerRef}
      className="fixed z-50 w-[400px] glass-morphism flex flex-col"
      style={{
        left: `${x}px`,
        top: `${y}px`,
        maxHeight: '500px',
        boxShadow: 'var(--shadow-float)',
        backgroundColor: 'var(--glass-light)',
        backdropFilter: 'var(--glass-blur)',
        border: '1px solid rgba(255, 255, 255, 0.2)'
      }}
    >
      {/* 头部 */}
      <div 
        className="flex items-center justify-between p-4 border-b divider-soft"
        style={{
          backgroundColor: 'rgba(255, 255, 255, 0.1)',
          backdropFilter: 'var(--glass-blur)'
        }}
      >
        <div className="flex items-center space-x-2">
          <Sparkles className="w-5 h-5" style={{ color: 'var(--accent)' }} />
          <span className="font-medium" style={{ color: 'var(--neutral-900)' }}>
            {context === 'original' ? '原文助手' : '笔记编辑助手'}
          </span>
        </div>
        <div className="flex items-center space-x-2">
          {/* 模式切换 */}
          <div 
            className="flex rounded-lg p-1"
            style={{ backgroundColor: 'var(--neutral-200)' }}
          >
            <button
              onClick={() => setMode('ask')}
              className={cn(
                "px-3 py-1 rounded text-sm transition-colors hover-lift",
                mode === 'ask' && "shadow-sm"
              )}
              style={{
                backgroundColor: mode === 'ask' ? 'white' : 'transparent',
                color: mode === 'ask' ? 'var(--neutral-900)' : 'var(--neutral-600)'
              }}
            >
              <MessageSquare className="w-4 h-4" />
            </button>
            <button
              onClick={() => setMode('agent')}
              className={cn(
                "px-3 py-1 rounded text-sm transition-colors hover-lift",
                mode === 'agent' && "shadow-sm"
              )}
              style={{
                backgroundColor: mode === 'agent' ? 'white' : 'transparent',
                color: mode === 'agent' ? 'var(--neutral-900)' : 'var(--neutral-600)'
              }}
              disabled={context === 'original'}
              title={context === 'original' ? '仅在笔记区域可用' : ''}
            >
              <Edit3 className="w-4 h-4" />
            </button>
          </div>
          <button
            onClick={onClose}
            className="p-1 rounded transition-colors hover-lift"
            style={{ color: 'var(--neutral-600)' }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = 'var(--neutral-200)'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = 'transparent'
            }}
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 选中文本提示 */}
      {selectedText && (
        <div 
          className="px-4 py-2 border-b divider-soft"
          style={{ backgroundColor: 'var(--primary-light)' }}
        >
          <div className="text-xs mb-1" style={{ color: 'var(--primary-dark)' }}>已选中文本：</div>
          <div className="text-sm line-clamp-2" style={{ color: 'var(--neutral-900)' }}>
            "{selectedText.text}"
          </div>
        </div>
      )}

      {/* 对话历史 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center py-8" style={{ color: 'var(--neutral-400)' }}>
            <Sparkles className="w-8 h-8 mx-auto mb-2" style={{ color: 'var(--neutral-400)' }} />
            <p className="text-sm">
              {mode === 'ask' 
                ? '问我任何关于这篇内容的问题'
                : '我可以帮你编辑和完善笔记'
              }
            </p>
          </div>
        )}
        
        {messages.map((message, index) => (
          <div
            key={index}
            className={cn(
              "flex",
              message.role === 'user' ? "justify-end" : "justify-start"
            )}
          >
            <div
              className={cn(
                "max-w-[80%] rounded-lg px-4 py-2",
                message.role === 'user' && "hover-lift"
              )}
              style={{
                backgroundColor: message.role === 'user' 
                  ? 'var(--primary)' 
                  : 'rgba(255, 255, 255, 0.9)',
                color: message.role === 'user' 
                  ? 'white' 
                  : 'var(--neutral-900)',
                backdropFilter: message.role === 'assistant' ? 'var(--glass-blur)' : 'none'
              }}
            >
              {message.role === 'assistant' ? (
                <SafeMarkdown className="text-sm">
                  {message.content}
                </SafeMarkdown>
              ) : (
                <p className="text-sm">{message.content}</p>
              )}
              
              {/* 显示修改记录 */}
              {message.modifications && message.modifications.length > 0 && (
                <div className="mt-2 pt-2 border-t divider-soft">
                  <div className="text-xs mb-1" style={{ color: 'var(--neutral-600)' }}>已应用修改：</div>
                  {message.modifications.map((mod, i) => (
                    <div key={i} className="text-xs rounded p-1 mb-1 diff-highlight">
                      <span className="font-medium">
                        {mod.type === 'insert' && '➕ 插入'}
                        {mod.type === 'replace' && '🔄 替换'}
                        {mod.type === 'delete' && '❌ 删除'}
                      </span>
                      {mod.reason && (
                        <span style={{ color: 'var(--neutral-600)' }} className="ml-1">- {mod.reason}</span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div 
              className="rounded-lg px-4 py-2"
              style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)' }}
            >
              <div className="flex space-x-1">
                <div className="w-2 h-2 rounded-full animate-bounce" style={{ backgroundColor: 'var(--accent)' }} />
                <div className="w-2 h-2 rounded-full animate-bounce delay-100" style={{ backgroundColor: 'var(--accent)' }} />
                <div className="w-2 h-2 rounded-full animate-bounce delay-200" style={{ backgroundColor: 'var(--accent)' }} />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* 输入区域 */}
      <div className="border-t divider-soft p-4">
        <form onSubmit={(e) => { e.preventDefault(); handleSend() }} className="flex space-x-2">
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            placeholder={mode === 'ask' ? "问个问题..." : "描述你想要的修改..."}
            className="flex-1 px-3 py-2 rounded-lg text-sm transition-all"
            style={{
              border: '1px solid var(--neutral-400)',
              backgroundColor: 'rgba(255, 255, 255, 0.9)',
              color: 'var(--neutral-900)'
            }}
            onFocus={(e) => {
              e.currentTarget.style.borderColor = 'var(--primary)'
              e.currentTarget.style.outline = '2px solid var(--primary-light)'
            }}
            onBlur={(e) => {
              e.currentTarget.style.borderColor = 'var(--neutral-400)'
              e.currentTarget.style.outline = 'none'
            }}
            disabled={isLoading}
          />
          <button
            type="submit"
            disabled={!input.trim() || isLoading}
            className={cn(
              "px-3 py-2 rounded-lg transition-all hover-lift",
              "disabled:cursor-not-allowed disabled:opacity-50"
            )}
            style={{
              backgroundColor: 'var(--primary)',
              color: 'white'
            }}
          >
            <Send className="w-4 h-4" />
          </button>
        </form>
      </div>
    </div>
  )
}

export default FloatingAIAssistant