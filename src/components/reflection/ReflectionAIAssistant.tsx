'use client'

import React, { useState, useRef, useEffect } from 'react'
import { useSelectedNote, useAppStore } from '@/lib/store'
import { Send, Brain, Sparkles } from 'lucide-react'
import SafeMarkdown from '@/components/ui/SafeMarkdown'

interface ChatMessage {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

const ReflectionAIAssistant: React.FC = () => {
  const selectedNote = useSelectedNote()
  const { updateNote } = useAppStore()
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [input, setInput] = useState('')
  const [isStreaming, setIsStreaming] = useState(false)
  const [agentMode, setAgentMode] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 自动滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // 当选择的笔记改变时，清空对话历史
  useEffect(() => {
    setMessages([])
  }, [selectedNote?.id])

  // 发送消息
  const handleSendMessage = async () => {
    if (!input.trim() || !selectedNote || isStreaming) return

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: input.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInput('')
    setIsStreaming(true)

    // 创建AI回复的占位消息
    const assistantMessageId = (Date.now() + 1).toString()
    const assistantMessage: ChatMessage = {
      id: assistantMessageId,
      role: 'assistant',
      content: '',
      timestamp: new Date()
    }
    setMessages(prev => [...prev, assistantMessage])

    try {
      const response = await fetch('/api/reflection-chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: input.trim(),
          context: {
            originalContent: selectedNote.originalContent,
            structuredNotes: selectedNote.structuredNotes,
            integratedNotes: selectedNote.integratedNotes,
            title: selectedNote.title
          },
          history: messages.map(msg => ({
            role: msg.role,
            content: msg.content
          })),
          mode: agentMode ? 'agent' : 'chat'
        })
      })

      if (!response.ok) {
        throw new Error('Failed to get AI response')
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('No response body')
      }

      let accumulatedContent = ''
      
      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6)
            if (data === '[DONE]') continue

            try {
              const parsed = JSON.parse(data)
              
              if (parsed.choices?.[0]?.delta?.content) {
                // 常规聊天内容
                const content = parsed.choices[0].delta.content
                accumulatedContent += content
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { ...msg, content: accumulatedContent }
                    : msg
                ))
              } else if (parsed.type === 'note_update' && agentMode) {
                // Agent模式：笔记更新
                const { updates } = parsed
                if (updates && selectedNote) {
                  try {
                    await updateNote(selectedNote.id, updates)
                    // 添加一条系统消息表示更新成功
                    accumulatedContent += '\n\n✅ 笔记已更新'
                    setMessages(prev => prev.map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedContent }
                        : msg
                    ))
                  } catch (error) {
                    console.error('更新笔记失败:', error)
                    accumulatedContent += '\n\n❌ 笔记更新失败'
                    setMessages(prev => prev.map(msg => 
                      msg.id === assistantMessageId 
                        ? { ...msg, content: accumulatedContent }
                        : msg
                    ))
                  }
                }
              } else if (parsed.content) {
                // 直接内容流式更新
                accumulatedContent += parsed.content
                setMessages(prev => prev.map(msg => 
                  msg.id === assistantMessageId 
                    ? { ...msg, content: accumulatedContent }
                    : msg
                ))
              }
            } catch (e) {
              // 忽略解析错误
            }
          }
        }
      }
    } catch (error) {
      console.error('Error sending message:', error)
      setMessages(prev => prev.map(msg => 
        msg.id === assistantMessageId 
          ? { ...msg, content: '抱歉，发生了错误，请稍后重试。' }
          : msg
      ))
    } finally {
      setIsStreaming(false)
    }
  }

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  if (!selectedNote) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center mb-4">
            <Brain className="w-8 h-8 text-gray-400" />
          </div>
          <p className="text-gray-500">选择一个笔记开始对话</p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      {/* 头部 */}
      <div className="p-6 border-b border-gray-200/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="font-semibold text-gray-900">AI 助手</h2>
              <p className="text-sm text-gray-500">
                {agentMode ? '智能修改模式：AI 可直接修改笔记' : '对话模式：基于笔记内容的智能对话'}
              </p>
            </div>
          </div>
          
          {/* Agent模式切换 */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-600">Agent</label>
            <button
              onClick={() => setAgentMode(!agentMode)}
              className={`w-10 h-6 rounded-full transition-colors ${
                agentMode ? 'bg-blue-500' : 'bg-gray-300'
              }`}
            >
              <div
                className={`w-4 h-4 bg-white rounded-full transition-transform ${
                  agentMode ? 'translate-x-5' : 'translate-x-1'
                }`}
              />
            </button>
          </div>
        </div>
      </div>

      {/* 对话区域 */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-3">
              <Brain className="w-6 h-6 text-blue-600" />
            </div>
            <p className="text-gray-500 text-sm mb-4">开始与AI讨论这个笔记，系统会自动搜索知识库中的相关内容</p>
            <div className="space-y-2">
              {agentMode ? (
                // Agent模式下的推荐问题
                <>
                  <button
                    onClick={() => setInput('请帮我优化这个笔记的结构和内容')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    请帮我优化这个笔记的结构和内容
                  </button>
                  <button
                    onClick={() => setInput('添加更多相关的知识点和例子')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    添加更多相关的知识点和例子
                  </button>
                  <button
                    onClick={() => setInput('重新整理标题和总结这个笔记')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    重新整理标题和总结这个笔记
                  </button>
                </>
              ) : (
                // 对话模式下的推荐问题
                <>
                  <button
                    onClick={() => setInput('这个笔记的核心观点是什么？')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    这个笔记的核心观点是什么？
                  </button>
                  <button
                    onClick={() => setInput('如何将这些知识应用到实际工作中？')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    如何将这些知识应用到实际工作中？
                  </button>
                  <button
                    onClick={() => setInput('还有哪些相关的知识点值得深入了解？')}
                    className="block w-full text-left px-3 py-2 text-sm text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                  >
                    还有哪些相关的知识点值得深入了解？
                  </button>
                </>
              )}
            </div>
          </div>
        ) : (
          messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-100 text-gray-900'
                }`}
              >
                {message.role === 'assistant' ? (
                  <SafeMarkdown className="prose prose-sm max-w-none prose-p:my-1 prose-headings:my-2">
                    {message.content}
                  </SafeMarkdown>
                ) : (
                  <p className="text-sm">{message.content}</p>
                )}
              </div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入区域 */}
      <div className="p-4 border-t border-gray-200/50">
        <div className="flex space-x-2">
          <div className="flex-1">
            <textarea
              ref={inputRef}
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={agentMode ? "输入修改指令，AI将直接更新笔记..." : "输入你的问题..."}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 resize-none"
              rows={2}
              disabled={isStreaming}
            />
            {agentMode && (
              <p className="text-xs text-orange-600 mt-1">
                ⚠️ Agent模式：AI会直接修改你的笔记，请谨慎使用
              </p>
            )}
          </div>
          <button
            onClick={handleSendMessage}
            disabled={!input.trim() || isStreaming}
            className={`px-4 py-2 text-white rounded-lg disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95 ${
              agentMode 
                ? 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600'
                : 'bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700'
            }`}
          >
            <Send size={16} />
          </button>
        </div>
      </div>
    </div>
  )
}

export default ReflectionAIAssistant
